<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <header class="bg-white border-b sticky top-0 z-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <button
            routerLink="/playlists"
            class="text-gray-400 hover:text-gray-600 p-2 rounded-lg transition-colors"
          >
            <span class="material-icons">arrow_back</span>
          </button>
          <div class="ml-4">
            <div class="flex items-center gap-3">
              <h1 class="text-xl font-medium text-gray-900">
                {{ playlist?.name }}
              </h1>
              <!-- Color Indicator -->
              <button
                class="w-6 h-6 rounded-full border-2 border-white shadow-sm cursor-pointer hover:scale-110 transition-transform"
                [style.background-color]="playlist?.color || '#3B82F6'"
                [title]="'Click to change playlist color: ' + (playlist?.color || '#3B82F6')"
                (click)="openColorPicker()">
              </button>
            </div>
            <p class="text-sm text-gray-500">{{ playlist?.description }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <div
            class="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 rounded-lg"
          >
            <div
              class="w-2.5 h-2.5 rounded-full"
              [class]="getStatusColor(playlist?.status)"
            ></div>
            <span class="text-sm font-medium capitalize">{{
              playlist?.status
            }}</span>
          </div>
          <button
            (click)="previewPlaylist()"
            class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors flex items-center gap-2"
          >
            <span class="material-icons text-sm">play_arrow</span>
            Preview
          </button>
          <button
            class="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            (click)="publishPlaylist()"
          >
            <span class="material-icons text-sm">{{
              playlist?.status === "active" ? "unpublished" : "publish"
            }}</span>
            {{ playlist?.status === "active" ? "Unpublish" : "Publish" }}
          </button>
        </div>
      </div>
    </div>
  </header>

  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-3 gap-6">
      <!-- Left Column - Playlist Items -->
      <div class="col-span-2 space-y-6">
        <!-- Add Content Button -->
        <div
          class="bg-white rounded-lg border-2 border-dashed border-gray-200 p-8 text-center hover:border-blue-200 transition-colors"
        >
          <button
            (click)="showAddContent = true"
            class="inline-flex items-center px-6 py-3 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors gap-2"
          >
            <span class="material-icons">add</span>
            Add Content
          </button>
          <p class="mt-2 text-sm text-gray-500">
            Drag and drop to reorder items
          </p>
        </div>

        <!-- Playlist Items -->
        <div cdkDropList (cdkDropListDropped)="drop($event)" class="space-y-3">
          @for (item of playlist?.items; track item.id) {
          <div
            cdkDrag
            class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 playlist-item"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div 
                  class="w-16 h-16 bg-gray-50 rounded-lg overflow-hidden cursor-pointer"
                  (click)="showItemPreview(item)"
                >
                  @if (item.type === 'video') {
                    @if (item.content.thumbnail) {
                      <img 
                        [src]="item.content.thumbnail" 
                        [alt]="item.name"
                        class="w-full h-full object-cover"
                      />
                    } @else {
                      <div class="w-full h-full flex items-center justify-center">
                        <span class="material-icons text-gray-400">movie</span>
                      </div>
                    }
                  } @else if (item.type === 'image') {
                    <img 
                      [src]="item.content.url" 
                      [alt]="item.name"
                      class="w-full h-full object-cover"
                    />
                  } @else {
                    <div class="w-full h-full flex items-center justify-center">
                      <span class="material-icons text-gray-400">web</span>
                    </div>
                  }
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">{{ item.name }}</h3>
                  <div class="flex items-center gap-3 text-sm text-gray-500">
                    <span class="capitalize">{{ item.type }}</span>
                    <span>•</span>
                    <span>{{ formatDuration(item.duration) }}</span>
                  </div>
                </div>
              </div>

              <div
                class="flex items-center space-x-2 opacity-0 hover:opacity-100 transition-opacity duration-200"
              >
                <button
                  (click)="editItem(item)"
                  class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg"
                  title="Edit"
                >
                  <span class="material-icons">edit</span>
                </button>
                <button
                  (click)="deleteItem(item)"
                  class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg"
                  title="Delete"
                >
                  <span class="material-icons">delete</span>
                </button>
                <div
                  cdkDragHandle
                  class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg cursor-grab drag-handle"
                  title="Drag to reorder"
                >
                  <span class="material-icons">drag_handle</span>
                </div>
              </div>
            </div>
          </div>
          }
        </div>
      </div>

      <!-- Right Column - Settings -->
      <div class="space-y-6">
        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-medium mb-6">Playlist Settings</h2>
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Transition Effect</label
              >
              <select
                [(ngModel)]="playlist!.settings.transition.type"
                class="w-full p-2.5 bg-gray-50 border-0 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="fade">Fade</option>
                <option value="slide">Slide</option>
                <option value="none">None</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Stats Card -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-medium mb-4">Playlist Stats</h2>
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 p-3 rounded-lg">
              <p class="text-sm text-gray-500">Total Items</p>
              <p class="text-2xl font-light">{{ playlist?.items?.length }}</p>
            </div>
            <div class="bg-gray-50 p-3 rounded-lg">
              <p class="text-sm text-gray-500">Duration</p>
              <p class="text-2xl font-light">
                {{ formatDuration(getTotalDuration()) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>

@if (showPreviewDialog && selectedPlaylist) {
<app-playlist-preview-dialog
  [playlist]="selectedPlaylist"
  (closeDialog)="closePreviewDialog()"
/> 
} 

@if (showAddContent) {
<app-add-content-dialog
  (onAdd)="handleAddContent($event)"
  (onCancel)="showAddContent = false"
/>
}

@if (showEditDialog && editingItem) {
  <app-edit-playlist-item-dialog
    [item]="editingItem"
    (save)="handleEditSave($event)"
    (cancel)="showEditDialog = false; editingItem = null"
  />
}

@if (showHoverModal && hoverItem) {
  <app-playlist-hover-modal
    [item]="hoverItem"
    (close)="closeHoverModal()"
  />
}

<!-- Color Picker Modal -->
@if (colorPickerModal.show) {
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Choose Playlist Color</h3>
        <button (click)="closeColorPicker()" class="text-gray-400 hover:text-gray-600">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-3">{{ playlist?.name }}</p>
        
        <!-- Predefined Colors -->
        <div class="grid grid-cols-8 gap-2 mb-4">
          @for (color of predefinedColors; track color) {
            <div class="w-8 h-8 rounded-full cursor-pointer border-2 border-gray-200 hover:border-gray-400 transition-colors"
              [style.background-color]="color"
              [class.ring-2]="colorPickerModal.selectedColor === color"
              [class.ring-blue-500]="colorPickerModal.selectedColor === color"
              (click)="selectColor(color)">
            </div>
          }
        </div>
        
        <!-- Custom Color Input -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Custom Color</label>
          <div class="flex items-center space-x-2">
            <input type="color" 
              [(ngModel)]="colorPickerModal.selectedColor"
              class="w-12 h-8 rounded border border-gray-300 cursor-pointer">
            <input type="text" 
              [(ngModel)]="colorPickerModal.selectedColor"
              placeholder="#3B82F6"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>
        
        <!-- Preview -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
          <div class="flex items-center p-3 bg-gray-50 rounded-md">
            <div class="w-4 h-4 rounded-full mr-3 border-2 border-white shadow-sm"
              [style.background-color]="colorPickerModal.selectedColor">
            </div>
            <span class="text-sm text-gray-700">{{ playlist?.name }}</span>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end space-x-3">
        <button (click)="closeColorPicker()" 
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
          Cancel
        </button>
        <button (click)="savePlaylistColor()" 
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
          Save Color
        </button>
      </div>
    </div>
  </div>
}