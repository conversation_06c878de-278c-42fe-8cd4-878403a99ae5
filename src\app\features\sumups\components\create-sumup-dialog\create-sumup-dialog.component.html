<!-- create-sumup-dialog.component.html -->
<div
  class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
>
  <div class="w-full max-w-md p-6 bg-white rounded-xl shadow-xl">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-medium text-gray-900">Create New Sumup</h2>
      <button
        (click)="onCancel.emit()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <span class="material-icons">close</span>
      </button>
    </div>

    <form
      [formGroup]="sumupForm"
      (ngSubmit)="handleSubmit()"
      class="space-y-5"
    >
      <!-- Error Message -->
      @if (error) {
        <div class="bg-red-50 border-l-4 border-red-400 p-4 text-sm text-red-700">
          <div class="flex">
            <div class="flex-shrink-0">
              <span class="material-icons text-red-400">error_outline</span>
            </div>
            <div class="ml-3">
              <p>{{ error }}</p>
            </div>
          </div>
        </div>
      }

      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
          Name *
        </label>
        <input
          type="text"
          id="name"
          formControlName="name"
          placeholder="Enter sumup name"
          class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 text-base placeholder:text-gray-400"
          required
        />
        @if (sumupForm.get('name')?.touched && sumupForm.get('name')?.errors?.['required']) {
          <p class="mt-1 text-sm text-red-600">Name is required</p>
        }
        @if (sumupForm.get('name')?.touched && sumupForm.get('name')?.errors?.['minlength']) {
          <p class="mt-1 text-sm text-red-600">Name must be at least 3 characters</p>
        }
      </div>

      <div>
        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          id="description"
          formControlName="description"
          placeholder="Add description (optional)"
          rows="2"
          class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none text-base placeholder:text-gray-400"
        ></textarea>
      </div>

      <div>
        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
          Status
        </label>
        <select
          id="status"
          formControlName="status"
          class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 text-base"
        >
          <option value="draft">Draft</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      <div class="pt-4 flex justify-end gap-3">
        <button
          type="button"
          (click)="onCancel.emit()"
          class="px-6 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          [disabled]="!sumupForm.valid || isSubmitting"
          class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
        >
          @if (isSubmitting) {
            <span class="material-icons animate-spin text-sm">refresh</span>
          }
          Create Sumup
        </button>
      </div>
    </form>
  </div>
</div>