<div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
  <form [formGroup]="scheduleForm" (ngSubmit)="handleSubmit()" class="space-y-6">
    <!-- Schedule Type Toggle -->
    <div class="flex items-center space-x-4">
      <span class="text-sm font-medium text-gray-700">Schedule Type:</span>
      <div class="flex items-center space-x-2">
        <label class="inline-flex items-center">
          <input
            type="radio"
            formControlName="schedule_type"
            [value]="'weekly'"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500"
          >
          <span class="ml-2 text-sm text-gray-700">Weekly</span>
        </label>
        <label class="inline-flex items-center ml-4">
          <input
            type="radio"
            formControlName="schedule_type"
            [value]="'specific_date'"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500"
          >
          <span class="ml-2 text-sm text-gray-700">Specific Date</span>
        </label>
      </div>
    </div>

    <!-- Playlist Selection -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">Playlist</label>
      <select
        formControlName="playlist_id"
        class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      >
        <option value="">Select a playlist</option>
        @for (playlist of availablePlaylists; track playlist.id) {
          <option [value]="playlist.id">{{ playlist.name }}</option>
        }
      </select>
      @if (scheduleForm.get('playlist_id')?.touched && scheduleForm.get('playlist_id')?.errors?.['required']) {
        <p class="mt-1 text-sm text-red-600">Please select a playlist</p>
      }
    </div>

    <!-- Specific Date Input (Conditional) -->
    <div *ngIf="scheduleForm.get('schedule_type')?.value === 'specific_date'" class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
        <input
          type="date"
          formControlName="specific_date"
          class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
      </div>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
          <input
            type="time"
            formControlName="start_time"
            class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
          <input
            type="time"
            formControlName="end_time"
            class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
        </div>
      </div>
    </div>

    <!-- Weekly Schedule (Conditional) -->
    <div *ngIf="scheduleForm.get('schedule_type')?.value === 'weekly'" class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Days of Week</label>
        <div class="grid grid-cols-7 gap-2">
          @for (day of daysOfWeek; track day) {
            <div class="flex flex-col items-center">
              <label class="inline-flex items-center">
                <input
                  type="checkbox"
                  [checked]="isDaySelected(day)"
                  (change)="onDayChange($event)"
                  [value]="day"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
                <span class="ml-2 text-sm text-gray-700">{{ day.substring(0, 3) }}</span>
              </label>
            </div>
          }
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
          <input
            type="time"
            formControlName="start_time"
            class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
          <input
            type="time"
            formControlName="end_time"
            class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
        </div>
      </div>
    </div>

    <!-- Priority -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
      <select
        formControlName="priority"
        class="w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      >
        <option [value]="1">High</option>
        <option [value]="2">Medium</option>
        <option [value]="3">Low</option>
      </select>
    </div>

    <!-- Submit -->
    <div class="flex justify-end gap-2">
      <button
        type="button"
        (click)="onCancel.emit()"
        class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
      >
        Cancel
      </button>
      <button
        type="submit"
        [disabled]="!scheduleForm.valid || loading"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Add Schedule
      </button>
    </div>
  </form>
</div>