<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <!-- Add the resay logo -->
  <div class="sm:mx-auto sm:w-full sm:max-w-md mb-8 flex justify-center">
    <img 
      src="/image/resay-logo.webp"
      alt="Resay Logo"
      class="h-16 w-auto object-contain"
    />
  </div>
  
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="text-center text-3xl font-extrabold text-gray-900">
      Welcome to Digital Signage
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Sign in to your account
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow-2xl sm:rounded-lg sm:px-10 transform transition-all hover:scale-[1.01]">
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6">
        <!-- Success message -->
        @if (message) {
          <div class="bg-green-50 border-l-4 border-green-400 p-4 text-sm text-green-700">
            <div class="flex">
              <div class="flex-shrink-0">
                <span class="material-icons text-green-400">check_circle</span>
              </div>
              <div class="ml-3">
                <p>{{ message }}</p>
              </div>
            </div>
          </div>
        }

        <!-- Error message -->
        @if (error) {
          <div class="bg-red-50 border-l-4 border-red-400 p-4 text-sm text-red-700">
            <div class="flex">
              <div class="flex-shrink-0">
                <span class="material-icons text-red-400">error_outline</span>
              </div>
              <div class="ml-3">
                <p>{{ error }}</p>
              </div>
            </div>
          </div>
        }

        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">
            Email address
          </label>
          <div class="mt-1">
            <div class="relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="material-icons text-gray-400 text-sm">mail</span>
              </div>
              <input
                id="email"
                type="email"
                formControlName="email"
                required
                class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter your email"
              />
            </div>
          </div>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">
            Password
          </label>
          <div class="mt-1">
            <div class="relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="material-icons text-gray-400 text-sm">lock</span>
              </div>
              <input
                id="password"
                type="password"
                formControlName="password"
                required
                class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter your password"
              />
            </div>
          </div>
        </div>

        <div class="flex items-center justify-end">
          <a routerLink="/auth/reset-password" class="text-sm text-blue-600 hover:text-blue-500 hover:underline">
            Forgot your password?
          </a>
        </div>

        <div>
          <button
            type="submit"
            [disabled]="!loginForm.valid || loading"
            class="group relative w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            @if (loading) {
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <span class="material-icons animate-spin text-sm">refresh</span>
              </span>
            }
            {{ loading ? 'Signing in...' : 'Sign in' }}
          </button>
        </div>
      </form>

      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">
              New to the platform?
            </span>
          </div>
        </div>

        <div class="mt-6">
          <a
            routerLink="/auth/register"
            class="w-full flex justify-center py-2.5 px-4 border-2 border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Create an account
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
