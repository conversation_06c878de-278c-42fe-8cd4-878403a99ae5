import { Component, EventEmitter, Input, Output, OnInit, OnChanges, SimpleChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaylistService } from '../../../../playlists/services/playlist.service';
import { Playlist, PlaylistItem } from '../../../../../models/playlist.model';

@Component({
  selector: 'app-current-playlist-card',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Playlist Card: Only the Edit button triggers the onView event -->
    <div class="rounded-lg p-4" [style.background-color]="getPlaylistColorWithOpacity(0.1)">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="font-medium text-gray-900">{{ playlist?.name || 'Untitled Playlist' }}</h3>
          <p class="text-sm text-gray-500 mt-1">
            {{ playlist?.items?.length || 0 }} items • {{ formatDuration(playlist?.duration || 0) }}
          </p>
        </div>
        <div class="flex gap-2">
          <button
            (click)="onEditClick()"
            class="p-2 bg-white rounded-lg transition-colors"
            [style.color]="playlist?.color || '#3B82F6'"
            (mouseenter)="$event.target.style.backgroundColor = getPlaylistColorWithOpacity(0.1)"
            (mouseleave)="$event.target.style.backgroundColor = 'white'"
            title="Edit Playlist"
            *ngIf="playlist"
          >
            <span class="material-icons">edit</span>
          </button>
          <button
            (click)="playPlaylist()"
            class="p-2 bg-white rounded-lg transition-colors"
            [style.color]="playlist?.color || '#3B82F6'"
            (mouseenter)="$event.target.style.backgroundColor = getPlaylistColorWithOpacity(0.1)"
            (mouseleave)="$event.target.style.backgroundColor = 'white'"
            title="Preview Playlist"
            *ngIf="playlist"
          >
            <span class="material-icons">visibility</span>
          </button>
        </div>
      </div>
      
      <!-- Visual Preview of Playlist Content -->
      <div class="mt-4">
        @if (playlist?.items?.length) {
          <div class="relative h-40 rounded-lg overflow-hidden bg-white border" [style.border-color]="getPlaylistColorWithOpacity(0.2)">
            <!-- Slideshow Container -->
            <div class="relative w-full h-full">
              @for (item of playlist?.items; track item.id) {
                <div 
                  class="absolute inset-0 transition-opacity duration-500 flex items-center justify-center"
                  [style.opacity]="currentIndex === playlist?.items?.indexOf(item) ? '1' : '0'"
                >
                  @if (item.type === 'image' && item.content.url) {
                    <img
                      [src]="item.content.url"
                      [alt]="item.name"
                      class="w-full h-full object-cover"
                    />
                  } @else if (item.type === 'video' && item.content.thumbnail) {
                    <div class="relative w-full h-full">
                      <img
                        [src]="item.content.thumbnail"
                        [alt]="item.name"
                        class="w-full h-full object-cover"
                      />
                      <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-12 h-12 bg-black/50 rounded-full flex items-center justify-center">
                          <span class="material-icons text-white">play_arrow</span>
                        </div>
                      </div>
                    </div>
                  } @else {
                    <div class="w-full h-full flex flex-col items-center justify-center bg-gray-100">
                      <span class="material-icons text-4xl text-gray-400">
                        {{ getItemIcon(item.type) }}
                      </span>
                      <p class="text-sm text-gray-500 mt-2 max-w-[80%] truncate">{{ item.name }}</p>
                    </div>
                  }
                </div>
              }
            </div>

            <!-- Progress Indicators -->
            @if (playlist && playlist.items && playlist.items.length > 1) {
              <div class="absolute bottom-2 left-2 flex gap-1">
                @for (item of playlist.items; track item.id) {
                  <div 
                    class="w-2 h-2 rounded-full transition-all duration-300"
                    [style.background-color]="currentIndex === playlist.items.indexOf(item) ? 'rgb(59, 130, 246)' : 'rgba(255, 255, 255, 0.5)'"
                  ></div>
                }
              </div>
            }

            <!-- Item Counter -->
            <div class="absolute top-2 right-2 px-2 py-1 bg-black/50 text-white text-xs rounded">
              {{ currentIndex + 1 }} / {{ playlist?.items?.length }}
            </div>
          </div>
          
          <!-- Currently Displayed Item Info -->
          @if (playlist && playlist.items && playlist.items.length > 0) {
            <div class="mt-2 text-sm">
              <p class="font-medium text-gray-900 truncate">{{ playlist.items[currentIndex].name || 'Untitled Item' }}</p>
              <p class="text-gray-500">
                {{ playlist.items[currentIndex].type | titlecase }} • 
                {{ formatDuration(playlist.items[currentIndex].duration || 0) }}
              </p>
            </div>
          }
        } @else if (playlist) {
          <div class="h-40 rounded-lg bg-white border border-blue-100 flex flex-col items-center justify-center">
            <span class="material-icons text-4xl text-gray-400 mb-2">playlist_play</span>
            <p class="text-gray-500">This playlist is empty</p>
          </div>
        }
      </div>
    </div>
  `
})
export class CurrentPlaylistCardComponent implements OnInit, OnChanges, OnDestroy {
  @Input() screenId!: string;
  @Input() currentPlaylistId: string | null = null;
  @Output() onView = new EventEmitter<Playlist>(); // Emit when edit button is clicked
  @Output() onAssign = new EventEmitter<void>();
  @Output() onPlayStatusChange = new EventEmitter<boolean>();

  playlist: Playlist | null = null;
  loading = false;
  error: string | null = null;
  isPlaying = false;
  currentIndex = 0;
  private slideshowInterval: any;
  private readonly SLIDE_DURATION = 3000; // 3 seconds
  private cachedPlaylist: Playlist | null = null;
  private loadCompleted = false;

  constructor(private playlistService: PlaylistService) {}

  ngOnInit() {
    if (this.currentPlaylistId) {
      this.loadPlaylist();
    } else {
      this.resetState();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    // Only reload playlist if the currentPlaylistId actually changed
    if (changes['currentPlaylistId'] && 
        changes['currentPlaylistId'].currentValue !== changes['currentPlaylistId'].previousValue) {
      console.log('Current playlist ID changed:', this.currentPlaylistId);
      
      // Reset the load completed flag to ensure we get fresh data
      this.loadCompleted = false;
      
      if (this.currentPlaylistId) {
        // Clear current state before loading new data
        this.playlist = null;
        this.currentIndex = 0;
        this.stopSlideshow();
        
        // Load the new playlist
        this.loadPlaylist();
      } else {
        this.resetState();
      }
    }
  }

  ngOnDestroy() {
    this.stopSlideshow();
  }

  private resetState() {
    this.playlist = null;
    this.cachedPlaylist = null;
    this.currentIndex = 0;
    this.isPlaying = false;
    this.error = null;
    this.loadCompleted = false;
    this.stopSlideshow();
  }

  loadPlaylist() {
    if (!this.currentPlaylistId) {
      this.resetState();
      return;
    }

    // If we already completed loading once and have cached data, use that first
    if (this.loadCompleted && this.cachedPlaylist) {
      this.playlist = this.cachedPlaylist;
      if (this.playlist && this.playlist.items && this.playlist.items.length > 0) {
        this.startSlideshow();
      }
    }

    this.loading = true;
    this.error = null;
    console.log('Loading playlist with ID:', this.currentPlaylistId);

    this.playlistService.getPlaylist(this.currentPlaylistId).subscribe({
      next: (playlist) => {
        console.log('Playlist loaded:', playlist);
        
        // Deep clone the playlist to ensure we don't lose data
        this.cachedPlaylist = JSON.parse(JSON.stringify(playlist));
        
        // Only update the playlist reference if we don't already have cached data
        // or if this is our first load
        if (!this.loadCompleted) {
          this.playlist = playlist;
        }
        
        // Start slideshow if playlist has items
        if (playlist && playlist.items && playlist.items.length > 0) {
          this.startSlideshow();
          this.isPlaying = true;
        } else {
          this.isPlaying = false;
        }
        
        this.loading = false;
        this.loadCompleted = true;
      },
      error: (error) => {
        console.error('Error loading playlist:', error);
        this.error = 'Failed to load playlist';
        this.loading = false;
        
        // If we have a cached playlist, use that instead of resetting
        if (this.cachedPlaylist) {
          this.playlist = this.cachedPlaylist;
          if (this.playlist && this.playlist.items && this.playlist.items.length > 0) {
            this.startSlideshow();
          }
        } else {
          this.resetState();
        }
      }
    });
  }

  private startSlideshow() {
    if (!this.playlist || !this.playlist.items || this.playlist.items.length <= 1) return;
    
    // Clear any existing interval first
    this.stopSlideshow();
    
    this.slideshowInterval = setInterval(() => {
      if (this.playlist && this.playlist.items && this.playlist.items.length) {
        this.currentIndex = (this.currentIndex + 1) % this.playlist.items.length;
      }
    }, this.SLIDE_DURATION);
  }

  private stopSlideshow() {
    if (this.slideshowInterval) {
      clearInterval(this.slideshowInterval);
      this.slideshowInterval = null;
    }
  }

  playPlaylist() {
    this.isPlaying = true;
    this.onPlayStatusChange.emit(true);
  }

  pausePlaylist() {
    this.isPlaying = false;
    this.onPlayStatusChange.emit(false);
  }

  getItemIcon(type: string): string {
    switch (type) {
      case 'video': return 'movie';
      case 'image': return 'image';
      case 'webpage': return 'web';
      case 'ticker': return 'text_snippet';
      default: return 'insert_drive_file';
    }
  }

  formatDuration(seconds: number): string {
    if (isNaN(seconds) || seconds < 0) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  onEditClick() {
    console.log('Edit button clicked in current playlist card');
    if (this.playlist) {
      this.onView.emit(this.playlist);
    }
  }

  getPlaylistColorWithOpacity(opacity: number): string {
    const color = this.playlist?.color || '#3B82F6';
    // Convert hex to rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
}