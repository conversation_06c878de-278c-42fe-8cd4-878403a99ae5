import { Component, Input, OnInit, OnDestroy, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Playlist, PlaylistItem } from '../../../models/playlist.model';

@Component({
  selector: 'app-tv-display',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tv-display.component.html',
  styleUrls: ['./tv-display.component.scss']
})
export class TvDisplayComponent implements OnInit, OnDestroy, OnChanges {
  @Input() currentPlaylist: Playlist | null = null;
  @Input() isPlaying: boolean = false;
  @Input() screenOrientation: 'portrait' | 'landscape' = 'landscape';
  @Input() compact: boolean = false;

  currentItem: PlaylistItem | null = null;
  currentItemIndex: number = 0;
  playbackProgress: number = 0;
  error: string | null = null;
  
  private mediaTimer: any;
  private progressTimer: any;
  private isInitialized = false;

  get isPortrait(): boolean {
    return this.screenOrientation === 'portrait';
  }

  ngOnInit() {
    this.initializePlayback();
  }

  ngOnDestroy() {
    this.stopPlayback();
  }

  ngOnChanges(changes: SimpleChanges) {
    // Check if currentPlaylist or isPlaying has changed
    if (changes['currentPlaylist'] || changes['isPlaying']) {
      // Special handling for when playlist becomes available after component initialization
      if (changes['currentPlaylist'] && 
          changes['currentPlaylist'].previousValue === null && 
          changes['currentPlaylist'].currentValue !== null) {
        // Reset the initialization flag to allow proper startup
        this.isInitialized = false;
      }
      
      // Always try to initialize playback when inputs change
      setTimeout(() => {
        this.initializePlayback();
      }, 0);
    }
  }

  initializePlayback() {
    this.error = null;
    this.stopPlayback();
    
    // If we're not playing and have no playlist, show offline state
    if (!this.isPlaying && !this.currentPlaylist) {
      return;
    }
    
    if (!this.currentPlaylist) {
      this.error = 'No playlist available';
      return;
    }
    
    if (!this.currentPlaylist.items || this.currentPlaylist.items.length === 0) {
      this.error = 'Playlist is empty';
      return;
    }
    
    // Mark as initialized when we have a valid playlist
    this.isInitialized = true;
    
    if (this.isPlaying) {
      this.startPlayback();
    }
  }

  private startPlayback() {
    // Check if we have a valid playlist with items, regardless of initialization state
    if (!this.currentPlaylist?.items?.length) {
      this.error = 'No items in playlist';
      return;
    }
    
    // Mark as initialized when we start playback
    this.isInitialized = true;
    
    try {
      this.currentItemIndex = 0;
      this.playCurrentMedia();
    } catch (err) {
      console.error('Error starting playback:', err);
      this.error = 'Failed to start playback';
    }
  }

  private playCurrentMedia() {
    if (!this.currentPlaylist?.items?.length) return;
    
    try {
      this.currentItem = this.currentPlaylist.items[this.currentItemIndex];
      if (!this.currentItem) {
        throw new Error('Invalid playlist item');
      }
      
      this.playbackProgress = 0;
      this.error = null;
      
      // Use the playlist item's duration (in seconds) converted to milliseconds
      const duration = Math.max(1, (this.currentItem.duration || 5)) * 1000; // Ensure at least 1 second
      
      // Start progress animation
      this.startProgressAnimation(duration);
      
      // Schedule next media
      this.mediaTimer = setTimeout(() => {
        this.nextMedia();
      }, duration);
      
    } catch (err) {
      console.error('Error playing media:', err);
      this.error = 'Error loading media';
      // Try to move to next item if current one fails
      this.scheduleNextMedia(2000); // Try next item after 2 seconds
    }
  }

  private nextMedia() {
    if (!this.currentPlaylist?.items?.length) return;
    
    try {
      this.clearTimers();
      
      // Move to next item, loop back to start if at end
      this.currentItemIndex = (this.currentItemIndex + 1) % this.currentPlaylist.items.length;
      this.playCurrentMedia();
      
    } catch (err) {
      console.error('Error moving to next media:', err);
      this.error = 'Error advancing playlist';
      this.scheduleNextMedia(2000); // Try again after delay
    }
  }
  
  private scheduleNextMedia(delay: number) {
    this.clearTimers();
    this.mediaTimer = setTimeout(() => {
      this.nextMedia();
    }, delay);
  }
  
  private clearTimers() {
    if (this.mediaTimer) {
      clearTimeout(this.mediaTimer);
      this.mediaTimer = null;
    }
    this.clearProgressTimer();
  }

  private startProgressAnimation(duration: number) {
    this.clearProgressTimer();
    
    const intervalTime = 100; // Update every 100ms
    const increment = (intervalTime / duration) * 100;
    
    this.progressTimer = setInterval(() => {
      this.playbackProgress += increment;
      if (this.playbackProgress >= 100) {
        this.playbackProgress = 100;
        this.clearProgressTimer();
      }
    }, intervalTime);
  }

  private clearProgressTimer() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
      this.progressTimer = null;
    }
  }
  
  private stopPlayback() {
    this.clearTimers();
    this.currentItem = null;
    this.playbackProgress = 0;
    this.currentItemIndex = 0; // Reset index when stopping
  }
}