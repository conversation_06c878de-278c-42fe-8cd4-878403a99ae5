import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-step-days-selection',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="space-y-6">
      <!-- Step Header -->
      <div class="text-center">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Select Days of the Week</h3>
        <p class="text-gray-600">Choose which days this schedule should be active</p>
      </div>


      <!-- Quick Selection Presets -->
      <div class="bg-gray-50 rounded-lg p-6">
        <h4 class="font-medium text-gray-900 mb-4">Quick Selection</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          @for (preset of dayPresets; track preset.label) {
            <button
              type="button"
              (click)="applyDayPreset(preset.days)"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-white hover:border-blue-300 transition-colors text-center"
            >
              <div class="font-medium text-gray-900">{{ preset.label }}</div>
              <div class="text-xs text-gray-600">{{ preset.description }}</div>
            </button>
          }
        </div>
      </div>

      <!-- Individual Day Selection -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="font-medium text-gray-900 mb-4">Individual Days</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          @for (day of availableDays; track day) {
            <label 
              class="flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50"
              [class]="getDayCardClass(day)"
            >
              <input
                type="checkbox"
                [checked]="isDaySelected(day)"
                (change)="toggleDay(day)"
                class="sr-only"
              />
              
              <!-- Custom Checkbox -->
              <div 
                class="flex-shrink-0 w-5 h-5 border-2 rounded flex items-center justify-center mr-3 transition-colors"
                [class]="isDaySelected(day) ? 'border-blue-600 bg-blue-600' : 'border-gray-300'"
              >
                @if (isDaySelected(day)) {
                  <span class="material-icons text-white text-sm">check</span>
                }
              </div>

              <!-- Day Info -->
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ day }}</div>
                <div class="text-sm text-gray-600">{{ getDayDescription(day) }}</div>
              </div>

              <!-- Day Icon -->
              <span class="material-icons text-gray-400 ml-2">
                {{ getDayIcon(day) }}
              </span>
            </label>
          }
        </div>
      </div>

      <!-- Selection Summary -->
      @if (localSelectedDays.length > 0) {
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-start">
            <span class="material-icons text-blue-600 mr-2 mt-0.5">check_circle</span>
            <div class="flex-1">
              <h4 class="font-medium text-blue-900">Selected Days</h4>
              <div class="mt-2">
                <div class="flex flex-wrap gap-2">
                  @for (day of localSelectedDays; track day) {
                    <span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                      {{ getShortDayName(day) }}
                      <button
                        type="button"
                        (click)="removeDay(day)"
                        class="ml-1 text-blue-600 hover:text-blue-800"
                        [attr.aria-label]="'Remove ' + day"
                      >
                        <span class="material-icons text-xs">close</span>
                      </button>
                    </span>
                  }
                </div>
                <p class="text-sm text-blue-700 mt-2">
                  Schedule will be active on {{ formatSelectedDays() }}
                </p>
              </div>
            </div>
          </div>
        </div>
      }

      <!-- Schedule Pattern Preview -->
      @if (localSelectedDays.length > 0) {
        <div class="bg-gray-50 rounded-lg p-6">
          <h4 class="font-medium text-gray-900 mb-4">Weekly Schedule Preview</h4>
          <div class="grid grid-cols-7 gap-2">
            @for (day of availableDays; track day) {
              <div class="text-center">
                <div class="text-xs font-medium text-gray-600 mb-2">
                  {{ getShortDayName(day) }}
                </div>
                <div 
                  class="w-full h-8 rounded border-2 flex items-center justify-center text-xs font-medium transition-colors"
                  [class]="isDaySelected(day) 
                    ? 'border-blue-600 bg-blue-600 text-white' 
                    : 'border-gray-200 bg-gray-100 text-gray-400'"
                >
                  @if (isDaySelected(day)) {
                    <span class="material-icons text-xs">check</span>
                  } @else {
                    <span>—</span>
                  }
                </div>
              </div>
            }
          </div>
        </div>
      }

      <!-- Clear Selection -->
      @if (localSelectedDays.length > 0) {
        <div class="text-center">
          <button
            type="button"
            (click)="clearSelection()"
            class="text-sm text-gray-600 hover:text-red-600 transition-colors"
          >
            Clear all selections
          </button>
        </div>
      }
    </div>
  `
})
export class StepDaysSelectionComponent implements OnInit {
  @Input() availableDays: string[] = [];
  @Input() selectedDays: string[] = [];
  @Input() error: string | undefined;
  @Output() daysSelected = new EventEmitter<string[]>();

  localSelectedDays: string[] = [];

  dayPresets = [
    {
      label: 'Weekdays',
      description: 'Mon - Fri',
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    },
    {
      label: 'Weekends',
      description: 'Sat - Sun',
      days: ['Saturday', 'Sunday']
    },
    {
      label: 'All Week',
      description: 'Every day',
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    {
      label: 'Custom',
      description: 'Select manually',
      days: []
    }
  ];

  ngOnInit(): void {
    this.localSelectedDays = [...this.selectedDays];
  }

  ngOnChanges(): void {
    this.localSelectedDays = [...this.selectedDays];
  }

  isDaySelected(day: string): boolean {
    return this.localSelectedDays.includes(day);
  }

  toggleDay(day: string): void {
    if (this.isDaySelected(day)) {
      this.removeDay(day);
    } else {
      this.addDay(day);
    }
  }

  addDay(day: string): void {
    if (!this.isDaySelected(day)) {
      this.localSelectedDays.push(day);
      this.emitDaysSelected();
    }
  }

  removeDay(day: string): void {
    this.localSelectedDays = this.localSelectedDays.filter(d => d !== day);
    this.emitDaysSelected();
  }

  applyDayPreset(days: string[]): void {
    this.localSelectedDays = [...days];
    this.emitDaysSelected();
  }

  clearSelection(): void {
    this.localSelectedDays = [];
    this.emitDaysSelected();
  }

  private emitDaysSelected(): void {
    this.daysSelected.emit([...this.localSelectedDays]);
  }

  getDayCardClass(day: string): string {
    return this.isDaySelected(day)
      ? 'border-blue-500 bg-blue-50'
      : 'border-gray-200 hover:border-gray-300';
  }

  getDayDescription(day: string): string {
    const descriptions: { [key: string]: string } = {
      'Monday': 'Start of work week',
      'Tuesday': 'Mid-week day',
      'Wednesday': 'Hump day',
      'Thursday': 'Almost weekend',
      'Friday': 'End of work week',
      'Saturday': 'Weekend day',
      'Sunday': 'Rest day'
    };
    return descriptions[day] || '';
  }

  getDayIcon(day: string): string {
    const icons: { [key: string]: string } = {
      'Monday': 'work',
      'Tuesday': 'work',
      'Wednesday': 'work',
      'Thursday': 'work',
      'Friday': 'work',
      'Saturday': 'weekend',
      'Sunday': 'home'
    };
    return icons[day] || 'calendar_today';
  }

  getShortDayName(day: string): string {
    return day.substring(0, 3);
  }

  formatSelectedDays(): string {
    if (this.localSelectedDays.length === 0) return 'no days';
    if (this.localSelectedDays.length === 7) return 'every day';
    if (this.localSelectedDays.length === 1) return this.localSelectedDays[0];
    
    const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const weekends = ['Saturday', 'Sunday'];
    
    const selectedWeekdays = this.localSelectedDays.filter(day => weekdays.includes(day));
    const selectedWeekends = this.localSelectedDays.filter(day => weekends.includes(day));
    
    if (selectedWeekdays.length === 5 && selectedWeekends.length === 0) {
      return 'weekdays';
    }
    if (selectedWeekdays.length === 0 && selectedWeekends.length === 2) {
      return 'weekends';
    }
    if (selectedWeekdays.length === 5 && selectedWeekends.length === 2) {
      return 'every day';
    }
    
    // For other combinations, list the days
    if (this.localSelectedDays.length <= 3) {
      return this.localSelectedDays.join(', ');
    } else {
      return `${this.localSelectedDays.slice(0, 2).join(', ')} and ${this.localSelectedDays.length - 2} more`;
    }
  }
}
