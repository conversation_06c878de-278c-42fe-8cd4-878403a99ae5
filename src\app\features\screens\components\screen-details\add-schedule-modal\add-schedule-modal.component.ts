import { 
  Component, 
  EventEmitter, 
  Input, 
  Output, 
  OnInit, 
  On<PERSON>estroy,
  HostListener,
  ViewChild,
  ElementRef,
  AfterViewInit
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { PlaylistSchedule } from '../../../../../models/screen.model';
import { Playlist } from '../../../../../models/playlist.model';
import { PlaylistService } from '../../../../playlists/services/playlist.service';

import { 
  ScheduleFormData, 
  ScheduleFormStep, 
  ScheduleFormErrors,
  SCHEDULE_FORM_STEPS,
  DAYS_OF_WEEK,
  PRIORITY_OPTIONS
} from './models/schedule-form.model';

import { StepPlaylistSelectionComponent } from './steps/step-playlist-selection.component';
import { StepTimeConfigurationComponent } from './steps/step-time-configuration.component';
import { StepDaysSelectionComponent } from './steps/step-days-selection.component';
import { StepReviewSubmitComponent } from './steps/step-review-submit.component';

@Component({
  selector: 'app-add-schedule-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    StepPlaylistSelectionComponent,
    StepTimeConfigurationComponent,
    StepDaysSelectionComponent,
    StepReviewSubmitComponent
  ],
  templateUrl: './add-schedule-modal.component.html',
  styleUrls: ['./add-schedule-modal.component.css']
})
export class AddScheduleModalComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() initialSchedule: PlaylistSchedule | null = null;
  @Output() onSubmit = new EventEmitter<PlaylistSchedule>();
  @Output() onCancel = new EventEmitter<void>();

  @ViewChild('modalContainer', { static: true }) modalContainer!: ElementRef;
  @ViewChild('firstFocusableElement', { static: false }) firstFocusableElement!: ElementRef;

  // Form and step management
  scheduleForm: FormGroup;
  currentStep = 1;
  steps: ScheduleFormStep[] = [...SCHEDULE_FORM_STEPS];
  
  // Data and state
  availablePlaylists: Playlist[] = [];
  formData: ScheduleFormData = {
    playlist_id: '',
    start_time: '',
    end_time: '',
    priority: 2,
    days_of_week: []
  };
  
  // UI state
  loading = false;
  submitting = false;
  errors: ScheduleFormErrors = {};
  
  // Constants
  readonly DAYS_OF_WEEK = DAYS_OF_WEEK;
  readonly PRIORITY_OPTIONS = PRIORITY_OPTIONS;
  
  private destroy$ = new Subject<void>();
  private previouslyFocusedElement: HTMLElement | null = null;

  constructor(
    private fb: FormBuilder,
    private playlistService: PlaylistService
  ) {
    this.scheduleForm = this.createForm();
  }

  ngOnInit(): void {
    this.previouslyFocusedElement = document.activeElement as HTMLElement;
    this.loadPlaylists();
    this.initializeForm();
    this.setupFormValidation();
  }

  ngAfterViewInit(): void {
    // Focus management for accessibility
    setTimeout(() => {
      if (this.firstFocusableElement?.nativeElement) {
        this.firstFocusableElement.nativeElement.focus();
      }
    }, 100);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    // Restore focus to previously focused element
    if (this.previouslyFocusedElement) {
      this.previouslyFocusedElement.focus();
    }
  }

  @HostListener('keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.handleCancel();
    }
    
    // Trap focus within modal
    if (event.key === 'Tab') {
      this.trapFocus(event);
    }
  }

  @HostListener('click', ['$event'])
  handleBackdropClick(event: MouseEvent): void {
    if (event.target === this.modalContainer?.nativeElement) {
      this.handleCancel();
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      playlist_id: [''],
      start_time: [''],
      end_time: [''],
      priority: [2],
      days_of_week: [[]]
    });
  }

  private initializeForm(): void {
    // No edit mode functionality
  }

  private setupFormValidation(): void {
    this.scheduleForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateFormData();
      });
  }

  private loadPlaylists(): void {
    this.loading = true;
    this.playlistService.getPlaylists()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (playlists) => {
          this.availablePlaylists = playlists;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading playlists:', error);
          this.errors.general = 'Failed to load playlists. Please try again.';
          this.loading = false;
        }
      });
  }

  private updateFormData(): void {
    const formValue = this.scheduleForm.value;
    this.formData = { ...this.formData, ...formValue };
    
    // Update playlist name if playlist_id changed
    if (formValue.playlist_id) {
      const selectedPlaylist = this.availablePlaylists.find(p => p.id === formValue.playlist_id);
      if (selectedPlaylist) {
        this.formData.playlist_name = selectedPlaylist.name;
      }
    }
  }

  private updateStepValidation(): void {
    this.steps[0].isValid = true;
    this.steps[1].isValid = true;
    this.steps[2].isValid = true;
    this.steps[3].isValid = true;
  }

  private trapFocus(event: KeyboardEvent): void {
    const focusableElements = this.modalContainer.nativeElement.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement.focus();
        event.preventDefault();
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement.focus();
        event.preventDefault();
      }
    }
  }

  // Public methods for step navigation
  canGoNext(): boolean {
    // Step 1: Playlist selection - require playlist_id
    if (this.currentStep === 1 && !this.formData.playlist_id) {
      return false;
    }
    
    // Step 2: Time configuration - require both start and end time
    if (this.currentStep === 2 && (!this.formData.start_time || !this.formData.end_time)) {
      return false;
    }
    
    // Step 3: Days selection - require at least one day
    if (this.currentStep === 3 && (!this.formData.days_of_week || this.formData.days_of_week.length === 0)) {
      return false;
    }
    
    return this.currentStep < this.steps.length;
  }

  canGoPrevious(): boolean {
    return this.currentStep > 1;
  }

  goToNext(): void {
    if (this.canGoNext()) {
      this.steps[this.currentStep - 1].isCompleted = true;
      this.currentStep++;
    }
  }

  goToPrevious(): void {
    if (this.canGoPrevious()) {
      this.currentStep--;
    }
  }

  goToStep(stepNumber: number): void {
    if (stepNumber >= 1 && stepNumber <= this.steps.length) {
      this.currentStep = stepNumber;
    }
  }

  handleSubmit(): void {
    if (!this.submitting) {
      this.submitting = true;
      
      const schedule: PlaylistSchedule = {
        playlist_id: this.formData.playlist_id,
        playlist_name: this.formData.playlist_name || '',
        start_time: this.formData.start_time,
        end_time: this.formData.end_time,
        priority: this.formData.priority,
        days_of_week: this.formData.days_of_week
      };
      
      // Simulate API call delay
      setTimeout(() => {
        this.onSubmit.emit(schedule);
        this.submitting = false;
      }, 500);
    }
  }

  handleCancel(): void {
    this.onCancel.emit();
  }

  // Event handlers for step components
  onPlaylistSelected(playlistId: string): void {
    this.scheduleForm.patchValue({ playlist_id: playlistId });
    this.updateFormData();
  }

  onTimeConfigured(timeData: { start_time: string; end_time: string; priority: number }): void {
    this.scheduleForm.patchValue(timeData);
    this.updateFormData();
  }

  onDaysSelected(days: string[]): void {
    this.scheduleForm.patchValue({ days_of_week: days });
    this.updateFormData();
  }

  // Helper methods for template - removed old step circle methods as we now use a progress bar
}
