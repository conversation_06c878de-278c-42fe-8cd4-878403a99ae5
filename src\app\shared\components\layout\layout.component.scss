:host {
  display: block;
  height: 100vh;
}

// Custom scrollbar for the sidebar
nav {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
  }
  
  &:hover::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
  }
}

// Active route styling with animation
.active-nav-link {
  background-color: rgb(239, 246, 255);
  color: rgb(37, 99, 235) !important;
  position: relative;
  
  .material-icons {
    color: rgb(37, 99, 235) !important;
  }

  &:hover {
    background-color: rgb(239, 246, 255);
  }

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: rgb(37, 99, 235);
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    animation: slideIn 0.2s ease-in-out;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Hover effect for nav items
nav a, nav button {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 100%;
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateX(-50%);
    transition: width 0.3s ease;
    z-index: -1;
    border-radius: 8px;
  }
  
  &:hover::after {
    width: 100%;
  }
  
  &:active {
    transform: translateY(1px);
    transition: transform 0.1s ease;
  }
}

// User profile section styling
.user-section {
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(249, 250, 251, 1);
  }
}