<div class="h-full flex flex-col bg-white">
  <!-- Header Section -->
  <div class="border-b border-gray-200 bg-gray-50 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option>Select Playlist</option>
          <option>Marketing Content</option>
          <option>Emergency Alerts</option>
          <option>Daily Announcements</option>
        </select>
        
        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option>All Areas</option>
          <option>Lobby</option>
          <option>Meeting Rooms</option>
          <option>Common Areas</option>
        </select>
        
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
          PUBLISH
        </button>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- Status Filter Buttons -->
        <button class="px-3 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-700 hover:bg-gray-300">
          Online
        </button>
        <button class="px-3 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-700 hover:bg-gray-300">
          Offline
        </button>
        <button class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 hover:bg-green-200">
          Active
        </button>
        <button class="px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200">
          Critical
        </button>
        <button class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700 hover:bg-yellow-200">
          Alert
        </button>
        <button class="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">
          Inactive
        </button>
        
        <div class="border-l border-gray-300 pl-2 ml-2">
          <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
            Clear alerts
          </button>
          <button class="text-blue-600 hover:text-blue-800 text-sm font-medium ml-4">
            Export
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Section -->
  <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
    <div class="flex items-center justify-between text-sm">
      <div class="flex items-center space-x-6">
        <span class="text-gray-600">
          <span class="font-medium text-gray-900">{{ selectedArea ? selectedArea.name : 'ALL AREAS' }}</span>
        </span>
        <span class="text-gray-600">
          <span class="font-medium text-gray-900">MONITORED PLAYERS ({{ screens.length }})</span>
        </span>
        <span class="text-gray-600">
          <span class="font-medium text-gray-900">LOCATION: {{ selectedArea ? selectedArea.location : 'Multiple' }}</span>
        </span>
      </div>
    </div>
  </div>

  <!-- Screen List -->
  <div class="p-4">
    <h4 class="text-sm font-medium text-gray-700 mb-3 px-1">Screens in this area</h4>
    <div class="space-y-2">
      @for (screen of filteredScreens; track screen.id) {
        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100 shadow-xs hover:shadow-sm transition-shadow cursor-pointer"
             (click)="onScreenClick(screen)"
             draggable="true"
             (dragstart)="onDragStart($event, screen)">
          <!-- Playlist Visualization (Left Side) -->
          <div class="flex items-center flex-shrink-0 w-24 h-16 mr-3 relative"
               (mouseenter)="onScreenHover(screen, $event)"
               (mouseleave)="onScreenLeave()">
            @if (screen.current_playlist && playlistMap[screen.current_playlist]) {
              <app-tv-display
                [currentPlaylist]="playlistMap[screen.current_playlist]"
                [isPlaying]="true"
                [screenOrientation]="getScreenOrientation(screen)"
                [compact]="true"
                class="w-full h-full"
              />
            } @else if (screen.current_playlist) {
              <div class="w-full h-full bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                <span class="material-icons text-gray-400 text-sm">hourglass_empty</span>
              </div>
            } @else {
              <div class="w-full h-full bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                <span class="material-icons text-gray-400 text-sm">playlist_add</span>
              </div>
            }
          </div>
          
          <div class="flex items-center min-w-0 flex-1">
            <div class="flex items-center min-w-0 flex-1">
              <div class="w-3 h-3 rounded-full flex-shrink-0 mr-3 mt-0.5 flex items-center justify-center"
                   [class]="getScreenStatusClass(screen) + (screen.status === 'online' && !isScreenOffline(screen) ? ' animate-pulse' : '')"
                   [title]="getScreenStatusText(screen)">
              </div>
              <div class="min-w-0 flex-1">
                <div class="font-medium text-gray-900 truncate">{{ screen.name }}</div>
                <div class="text-xs text-gray-500 mt-0.5 flex flex-wrap items-center gap-1">
                  <span>{{ screen.resolution }}</span>
                  <span>•</span>
                  <span class="capitalize">{{ screen.orientation || 'N/A' }}</span>
                </div>
              </div>
            </div>
            
            @if (screen.channel_name) {
              <span class="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full whitespace-nowrap ml-2 flex-shrink-0">
                {{ screen.channel_name }}
              </span>
            }
          </div>
        </div>
      }
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredScreens.length === 0" class="flex flex-col items-center justify-center py-12">
    <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
      <span class="material-icons text-xl text-gray-400">tv_off</span>
    </div>
    <h3 class="text-sm font-medium text-gray-900 mb-2">No screens found</h3>
    <p class="text-xs text-gray-500 text-center max-w-sm">
      No screens match your current filters. Try adjusting your search criteria.
    </p>
  </div>
  
  <!-- Screen Preview Modal -->
  <div *ngIf="screenPreview.show && screenPreview.screen" class="fixed inset-0 z-50 pointer-events-none">
    <div
      class="absolute screen-preview-modal pointer-events-auto"
      [style.left.px]="getModalLeft()" 
      [style.top.px]="getModalTop()" 
      (mouseleave)="onScreenLeave()">
      <div class="flex justify-between items-center mb-2 px-3 py-1 bg-gray-900 rounded-t">
        <h3 class="font-semibold text-white text-sm truncate pr-2">
          {{ screenPreview.screen.name }}
        </h3>
        <button (click)="onScreenLeave()" class="text-gray-400 hover:text-white">
          <span class="material-icons text-base">close</span>
        </button>
      </div>
      
      <!-- Screen Visualization -->
      <div class="screen-frame">
        <div class="screen-content aspect-video">
          <div class="absolute inset-0 bg-gradient-to-b from-transparent to-black opacity-30 z-10"></div>
          @if (screenPreview.screen.current_playlist && playlistMap[screenPreview.screen.current_playlist]) {
            <app-tv-display
              [currentPlaylist]="playlistMap[screenPreview.screen.current_playlist]"
              [isPlaying]="true"
              [screenOrientation]="getScreenOrientation(screenPreview.screen)"
              [compact]="true"
              class="w-full h-full hover-preview"
            />
          } @else if (screenPreview.screen.current_playlist) {
            <div class="w-full h-full flex items-center justify-center">
              <div class="text-center">
                <span class="material-icons text-gray-500 text-3xl">hourglass_empty</span>
                <p class="text-xs text-gray-400 mt-2">Loading...</p>
              </div>
            </div>
          } @else {
            <div class="w-full h-full flex items-center justify-center">
              <div class="text-center">
                <span class="material-icons text-gray-500 text-3xl">playlist_add</span>
                <p class="text-xs text-gray-400 mt-2">No playlist</p>
              </div>
            </div>
          }
        </div>
      </div>
      
      <div class="px-3 py-2 bg-gray-900 rounded-b text-xs text-gray-300 flex justify-between">
        <div class="flex items-center">
          <div class="w-2 h-2 rounded-full mr-2"
               [class]="getScreenStatusClass(screenPreview.screen)">
          </div>
          <span>{{ getScreenStatusText(screenPreview.screen) }}</span>
        </div>
        <span class="font-mono">{{ screenPreview.screen.resolution }}</span>
      </div>
    </div>
  </div>
</div>