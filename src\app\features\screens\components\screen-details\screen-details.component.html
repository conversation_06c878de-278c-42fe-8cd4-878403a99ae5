<div class="min-h-screen bg-gray-50">
  @if (loading) {
    <div class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>
  } @else if (error) {
    <div class="flex flex-col items-center justify-center min-h-screen">
      <span class="material-icons text-4xl text-gray-400 mb-2">error_outline</span>
      <h2 class="text-lg font-medium text-gray-900 mb-1">Error Loading Screen</h2>
      <p class="text-gray-500 mb-4">{{ error }}</p>
      <button 
        (click)="retryLoading()" 
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  } @else if (screen) {
    <app-screen-status-header [screen]="screen" />

    <main class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="space-y-4">  <!-- TV Display Preview -->
          <div class="bg-white rounded-lg shadow p-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="text-base font-medium">Live Preview</h3>
              <div class="flex items-center gap-1.5">
                <div [class]="'w-3 h-3 rounded-full ' + (isScreenOffline() ? 'bg-gray-400' : 'bg-green-500')"></div>
                <span class="text-sm text-gray-500">{{ isScreenOffline() ? 'Offline' : 'Online' }}</span>
              </div>
            </div>
            @if (!isScreenOffline() && screen.current_playlist && currentPlaylistData) {
              <app-tv-display
                [currentPlaylist]="currentPlaylistData"
                [isPlaying]="true"
                [screenOrientation]="screen.orientation || 'landscape'"
              />
            } @else if (!isScreenOffline() && screen.current_playlist && !currentPlaylistData) {
              <div class="bg-gray-50 rounded-md p-6 text-center h-48 flex flex-col items-center justify-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
                <p class="text-gray-500 text-sm">Loading playlist...</p>
              </div>
            } @else if (!isScreenOffline() && !screen.current_playlist) {
              <div class="bg-gray-50 rounded-md p-6 text-center h-48 flex flex-col items-center justify-center">
                <span class="material-icons text-gray-400 text-3xl mb-2">playlist_play</span>
                <p class="text-gray-500 text-sm">No playlist assigned</p>
              </div>
            } @else {
              <div class="bg-gray-50 rounded-md p-6 text-center h-48 flex flex-col items-center justify-center">
                <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <span class="material-icons text-gray-500 text-2xl">wifi_off</span>
                </div>
                <p class="text-gray-500 text-sm">Screen is offline</p>
                <p class="text-gray-400 text-xs mt-2" *ngIf="screen.last_ping">Last seen: {{ screen.last_ping | date:'short' }}</p>
              </div>
            }
          </div>
        </div>
        <div class="space-y-4">
          <!-- Screen Info and Actions -->

          <div class="border-t pt-3 mt-3">
            <label class="block text-xs text-gray-500 mb-1.5">Display Orientation</label>
            <button
              (click)="toggleOrientation()"
              [disabled]="isUpdatingOrientation"
              class="w-full flex items-center justify-center gap-1.5 px-3 py-1.5 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              @if (isUpdatingOrientation) {
                <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                <span>Updating...</span>
              } @else {
                <span class="material-icons text-sm">screen_rotation</span>
                <span>Switch to {{ screen.orientation === 'landscape' ? 'Portrait' : 'Landscape' }}</span>
              }
            </button>
            @if (screen.orientation === 'portrait') {
              <p class="text-[0.6rem] text-gray-500 mt-1.5 text-center">
                Display is in portrait mode
              </p>
            }
          </div>
          
          <app-screen-info-card 
            [screen]="screen"
          />
          
          <!-- Screen Actions Card -->
          <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-md font-medium mb-3">Screen Actions</h3>
            <div class="space-y-2">
              <button
                (click)="showDeleteScreenConfirm = true"
                class="w-full flex items-center justify-center gap-1.5 px-3 py-2 text-red-600 border border-red-200 rounded-md hover:bg-red-50 transition-colors text-sm"
              >
                <span class="material-icons text-sm">delete</span>
                Delete Screen
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-5 gap-0">
        <div class="lg:col-span-1"> <!-- Media Panel -->
          <app-media-library 
              [media$]="media$" 
              (mediaDragStart)="onMediaDragStart($event.event, $event.media)"
              (mediaDragEnd)="onMediaDragEnd($event)">
          </app-media-library>
        </div>

        <div class="lg:col-span-1"> <!-- Playlist Panel -->
          <app-playlist-panel
              [filteredPlaylists]="availablePlaylists"
              [expandedPlaylists]="getExpandedPlaylistsSet()"
              (playlistDragOver)="onPlaylistDragOver($event.event, $event.playlistId)"
              (playlistDragLeave)="onPlaylistDragLeave($event.event, $event.playlistId)"
              (playlistDrop)="onPlaylistDrop($event.event, $event.playlistId)"
              (toggleAvailable)="toggleAvailablePlaylist($event)"
              (colorPicker)="openColorPicker($event)"
              (dropPlaylistItem)="dropPlaylistItem($event.event, $event.playlist)"
              (playlistItemHover)="onPlaylistItemHover($event.item, $event.event)"
              (playlistItemLeave)="onPlaylistItemLeave()"
              (removeItem)="removeItemFromAvailablePlaylist($event.playlistId, $event.itemIndex)"
              (createPlaylist)="onCreatePlaylist()"
              (playlistDragEnd)="onPlaylistDragEnd($event)">
          </app-playlist-panel>
        </div>

        <div class="lg:col-span-3"> <!-- Schedule Panel -->
          <div class="bg-white rounded-none shadow-none overflow-hidden h-full">
            <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
              <h3 class="text-lg font-medium text-gray-900">Schedule Calendar</h3>
            </div>
            <div class="h-full min-h-[800px]">
              <app-schedule-calendar [schedules]="schedules"
                [selectedScreenName]="screen ? getSelectedScreenName() : null" 
                (addSchedule)="addScheduleEntry()"
                (deleteSchedule)="removeScheduleFromCalendar($event)"
                (scheduleUpdated)="onScheduleUpdated($event)"
                (playlistDropped)="onPlaylistDropped($event)">
              </app-schedule-calendar>
            </div>
          </div>
        </div>
      </div>
    </main>
  } @else {
    <div class="flex flex-col items-center justify-center min-h-screen">
      <span class="material-icons text-4xl text-gray-400 mb-2">screen_search_desktop</span>
      <h2 class="text-lg font-medium text-gray-900 mb-1">Screen not found</h2>
      <p class="text-gray-500">The screen you're looking for doesn't exist or has been removed.</p>
    </div>
  }
</div>

@if (showPlaylistDetails && screen && screen.current_playlist) {
  <app-playlist-details-dialog
    [playlistId]="screen.current_playlist"
    (onClose)="showPlaylistDetails = false"
  />
}

@if (showEditPlaylistDialog && selectedPlaylist) {
  <app-playlist-preview-dialog
    [playlist]="selectedPlaylist"
    (closeDialog)="closePreviewDialog()"
  />
}

<!-- Add Schedule Dialog -->
@if (showAddSchedule) {
  <div class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4">
      <!-- Dialog Header -->
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-xl font-semibold">Add Schedule</h2>
        <button 
          (click)="showAddSchedule = false"
          class="text-gray-400 hover:text-gray-600"
        >
          <span class="material-icons">close</span>
        </button>
      </div>

      <!-- Dialog Content -->
      <div class="p-6">
        <app-add-schedule-form
          [availablePlaylists]="availablePlaylists"
          (onSubmit)="handleAddSchedule($event)"
          (onCancel)="showAddSchedule = false"
        />
      </div>
    </div>
  </div>
}



<!-- Delete Screen Confirmation Dialog -->
@if (showDeleteScreenConfirm && screen) {
  <div class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
      <!-- Dialog Header -->
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-xl font-semibold text-gray-900">Delete Screen</h2>
        <button 
          (click)="showDeleteScreenConfirm = false"
          class="text-gray-400 hover:text-gray-600"
        >
          <span class="material-icons">close</span>
        </button>
      </div>

      <!-- Dialog Content -->
      <div class="p-6">
        <div class="flex items-center gap-3 mb-4 text-red-600">
          <span class="material-icons text-3xl">warning</span>
          <p class="text-lg font-medium">Are you sure?</p>
        </div>
        <p class="text-gray-600 mb-4">
          This will permanently delete the screen "<strong>{{ screen.name }}</strong>" and all its data. 
          This action cannot be undone.
        </p>

        <!-- Screen Details -->
        <div class="bg-gray-50 rounded-lg p-4 mb-4">
          <div class="flex flex-col gap-2">
            <div class="flex items-center gap-2">
              <span class="material-icons text-gray-400">monitor</span>
              <span class="text-sm text-gray-600">{{ screen.name }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="material-icons text-gray-400">location_on</span>
              <span class="text-sm text-gray-600">{{ screen.location.building || 'No location' }}</span>
            </div>
            @if (screen.current_playlist) {
              <div class="flex items-center gap-2">
                <span class="material-icons text-gray-400">playlist_play</span>
                <span class="text-sm text-gray-600">Currently playing playlist</span>
              </div>
            }
          </div>
        </div>

        <!-- Dialog Actions -->
        <div class="flex justify-end gap-3 mt-6">
          <button
            (click)="showDeleteScreenConfirm = false"
            class="px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            (click)="confirmDeleteScreen()"
            [disabled]="deletingScreen"
            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            @if (deletingScreen) {
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Deleting...
            } @else {
              <span class="material-icons text-sm">delete</span>
              Delete Screen
            }
          </button>
        </div>
      </div>
    </div>
  </div>
}

<!-- Create Playlist Dialog -->
@if (showCreatePlaylistDialog) {
  <app-create-playlist-dialog
    (onCreate)="handleCreatePlaylist()"
    (onCancel)="showCreatePlaylistDialog = false"
  />
}