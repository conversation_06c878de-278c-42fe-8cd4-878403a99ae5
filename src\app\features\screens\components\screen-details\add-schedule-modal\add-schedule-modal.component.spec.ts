import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { AddScheduleModalComponent } from './add-schedule-modal.component';
import { PlaylistService } from '../../../../playlists/services/playlist.service';
import { Playlist } from '../../../../../models/playlist.model';

describe('AddScheduleModalComponent - Validation', () => {
  let component: AddScheduleModalComponent;
  let fixture: ComponentFixture<AddScheduleModalComponent>;
  let mockPlaylistService: jasmine.SpyObj<PlaylistService>;

  const mockPlaylists: Playlist[] = [
    {
      id: '1',
      name: 'Test Playlist 1',
      description: 'Active playlist',
      status: 'active',
      duration: 300,
      items: [
        {
          id: '1',
          type: 'image',
          name: 'Test Image',
          duration: 5,
          content: { url: 'test.jpg', thumbnail: 'thumb.jpg' },
          settings: {
            transition: 'fade',
            transitionDuration: 1,
            scaling: 'fit'
          },
          schedule: null
        }
      ],
      lastModified: new Date().toISOString(),
      createdBy: 'test-user',
      settings: {
        autoPlay: true,
        loop: true,
        defaultMuted: false,
        transition: { type: 'fade', duration: 1 },
        defaultDuration: 5,
        scheduling: { enabled: false, priority: 1 }
      }
    },
    {
      id: '2',
      name: 'Empty Playlist',
      description: 'Playlist with no items',
      status: 'active',
      duration: 0,
      items: [],
      lastModified: new Date().toISOString(),
      createdBy: 'test-user',
      settings: {
        autoPlay: true,
        loop: true,
        defaultMuted: false,
        transition: { type: 'fade', duration: 1 },
        defaultDuration: 5,
        scheduling: { enabled: false, priority: 1 }
      }
    },
    {
      id: '3',
      name: 'Draft Playlist',
      description: 'Draft status playlist',
      status: 'draft',
      duration: 200,
      items: [
        {
          id: '2',
          type: 'video',
          name: 'Test Video',
          duration: 10,
          content: { url: 'test.mp4' },
          settings: {
            transition: 'slide',
            transitionDuration: 1,
            scaling: 'fit',
            muted: false,
            loop: false
          },
          schedule: null
        }
      ],
      lastModified: new Date().toISOString(),
      createdBy: 'test-user',
      settings: {
        autoPlay: true,
        loop: true,
        defaultMuted: false,
        transition: { type: 'fade', duration: 1 },
        defaultDuration: 5,
        scheduling: { enabled: false, priority: 1 }
      }
    }
  ];

  beforeEach(async () => {
    const playlistServiceSpy = jasmine.createSpyObj('PlaylistService', ['getPlaylists']);

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, AddScheduleModalComponent],
      providers: [
        { provide: PlaylistService, useValue: playlistServiceSpy }
      ]
    }).compileComponents();

    mockPlaylistService = TestBed.inject(PlaylistService) as jasmine.SpyObj<PlaylistService>;
    mockPlaylistService.getPlaylists.and.returnValue(of(mockPlaylists));

    fixture = TestBed.createComponent(AddScheduleModalComponent);
    component = fixture.componentInstance;
    component.availablePlaylists = mockPlaylists;
    fixture.detectChanges();
  });

  describe('Playlist Selection Validation', () => {
    it('should show error when no playlist is selected', () => {
      component.formData.playlist_id = '';
      component['validatePlaylistSelection']();
      
      expect(component.errors.playlist_id).toBe('Please select a playlist to continue');
    });

    it('should show error when selected playlist does not exist', () => {
      component.formData.playlist_id = 'non-existent';
      component['validatePlaylistSelection']();
      
      expect(component.errors.playlist_id).toBe('Selected playlist is no longer available');
    });

    it('should show error when selected playlist is not active', () => {
      component.formData.playlist_id = '3'; // Draft playlist
      component['validatePlaylistSelection']();
      
      expect(component.errors.playlist_id).toBe('Cannot schedule draft playlist. Please select an active playlist.');
    });

    it('should show error when selected playlist is empty', () => {
      component.formData.playlist_id = '2'; // Empty playlist
      component['validatePlaylistSelection']();
      
      expect(component.errors.playlist_id).toBe('Selected playlist is empty. Please choose a playlist with content.');
    });

    it('should pass validation for valid active playlist', () => {
      component.formData.playlist_id = '1'; // Valid active playlist
      component['validatePlaylistSelection']();
      
      expect(component.errors.playlist_id).toBeUndefined();
    });
  });

  describe('Time Configuration Validation', () => {
    it('should show error when start time is missing', () => {
      component.formData.start_time = '';
      component.formData.end_time = '10:00';
      component['validateTimeConfiguration']();
      
      expect(component.errors.start_time).toBe('Start time is required');
    });

    it('should show error when end time is missing', () => {
      component.formData.start_time = '09:00';
      component.formData.end_time = '';
      component['validateTimeConfiguration']();
      
      expect(component.errors.end_time).toBe('End time is required');
    });

    it('should show error when start and end times are the same', () => {
      component.formData.start_time = '09:00';
      component.formData.end_time = '09:00';
      component['validateTimeConfiguration']();
      
      expect(component.errors.end_time).toBe('Start and end times cannot be the same');
    });

    it('should show error for invalid time format', () => {
      component.formData.start_time = '25:00'; // Invalid hour
      component.formData.end_time = '10:00';
      component['validateTimeConfiguration']();
      
      expect(component.errors.start_time).toBe('Invalid time format');
    });

    it('should show error when schedule is too short', () => {
      component.formData.start_time = '09:00';
      component.formData.end_time = '09:00'; // Same time
      component['validateTimeConfiguration']();
      
      expect(component.errors.end_time).toBe('Start and end times cannot be the same');
    });

    it('should show error for invalid priority', () => {
      component.formData.start_time = '09:00';
      component.formData.end_time = '10:00';
      component.formData.priority = 0; // Invalid priority
      component['validateTimeConfiguration']();
      
      expect(component.errors.priority).toBe('Please select a valid priority level');
    });

    it('should pass validation for valid time range', () => {
      component.formData.start_time = '09:00';
      component.formData.end_time = '10:00';
      component.formData.priority = 2;
      component['validateTimeConfiguration']();
      
      expect(component.errors.start_time).toBeUndefined();
      expect(component.errors.end_time).toBeUndefined();
      expect(component.errors.priority).toBeUndefined();
    });

    it('should handle overnight schedules correctly', () => {
      component.formData.start_time = '22:00';
      component.formData.end_time = '06:00'; // Next day
      component.formData.priority = 2;
      component['validateTimeConfiguration']();
      
      expect(component.errors.start_time).toBeUndefined();
      expect(component.errors.end_time).toBeUndefined();
    });
  });

  describe('Days Selection Validation', () => {
    it('should show error when no days are selected', () => {
      component.formData.days_of_week = [];
      component['validateDaysSelection']();
      
      expect(component.errors.days_of_week).toBe('Please select at least one day of the week');
    });

    it('should show error for invalid day names', () => {
      component.formData.days_of_week = ['Monday', 'InvalidDay'];
      component['validateDaysSelection']();
      
      expect(component.errors.days_of_week).toBe('Invalid day(s) selected: InvalidDay');
    });

    it('should show error for duplicate days', () => {
      component.formData.days_of_week = ['Monday', 'Tuesday', 'Monday'];
      component['validateDaysSelection']();
      
      expect(component.errors.days_of_week).toBe('Duplicate days detected. Please review your selection.');
    });

    it('should pass validation for valid days selection', () => {
      component.formData.days_of_week = ['Monday', 'Tuesday', 'Wednesday'];
      component['validateDaysSelection']();
      
      expect(component.errors.days_of_week).toBeUndefined();
    });
  });

  describe('Helper Methods', () => {
    it('should validate time format correctly', () => {
      expect(component['isValidTimeFormat']('09:30')).toBe(true);
      expect(component['isValidTimeFormat']('23:59')).toBe(true);
      expect(component['isValidTimeFormat']('00:00')).toBe(true);
      expect(component['isValidTimeFormat']('25:00')).toBe(false);
      expect(component['isValidTimeFormat']('09:60')).toBe(false);
      expect(component['isValidTimeFormat']('invalid')).toBe(false);
    });

    it('should calculate duration correctly', () => {
      expect(component['calculateTimeDuration']('09:00', '10:00')).toBe(60); // 1 hour
      expect(component['calculateTimeDuration']('09:30', '10:00')).toBe(30); // 30 minutes
      expect(component['calculateTimeDuration']('22:00', '06:00')).toBe(480); // 8 hours overnight
    });

    it('should convert time to minutes correctly', () => {
      expect(component['timeToMinutes']('09:30')).toBe(570); // 9*60 + 30
      expect(component['timeToMinutes']('00:00')).toBe(0);
      expect(component['timeToMinutes']('23:59')).toBe(1439);
    });
  });

  describe('Real-time Validation', () => {
    it('should trigger validation when playlist is selected', () => {
      spyOn(component, 'validateCurrentStep' as any);
      
      component.onPlaylistSelected('1');
      
      expect(component['validateCurrentStep']).toHaveBeenCalled();
    });

    it('should trigger validation when time is configured', () => {
      spyOn(component, 'validateCurrentStep' as any);
      
      component.onTimeConfigured({ start_time: '09:00', end_time: '10:00', priority: 2 });
      
      expect(component['validateCurrentStep']).toHaveBeenCalled();
    });

    it('should trigger validation when days are selected', () => {
      spyOn(component, 'validateCurrentStep' as any);
      
      component.onDaysSelected(['Monday', 'Tuesday']);
      
      expect(component['validateCurrentStep']).toHaveBeenCalled();
    });
  });
});
