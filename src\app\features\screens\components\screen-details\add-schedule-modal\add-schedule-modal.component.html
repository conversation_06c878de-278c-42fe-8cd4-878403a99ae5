<!-- Mo<PERSON> Backdrop -->
<div
  #modalContainer
  class="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50 p-4"
  role="dialog"
  aria-modal="true"
  [attr.aria-labelledby]="'modal-title'"
>
  <!-- Modal Container -->
  <div class="bg-white rounded-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col modal-shadow">

    <!-- Modal Header -->
    <div class="px-8 py-6 flex items-center justify-between">
      <div class="flex-1">
        <h2 id="modal-title" class="text-2xl font-light text-gray-900 mb-1">
          Create Schedule
        </h2>
        <p class="text-gray-500 text-sm font-light">
          {{ steps[currentStep - 1].description }}
        </p>
      </div>

      <button
        #firstFocusableElement
        type="button"
        (click)="handleCancel()"
        class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-full transition-all duration-200"
        aria-label="Close modal"
      >
        <span class="material-icons text-lg">close</span>
      </button>
    </div>

    <!-- Progress Indicator -->
    <div class="px-8 pb-6">
      <!-- Progress Bar -->
      <div class="w-full bg-gray-100 rounded-full h-1 mb-4">
        <div
          class="bg-gray-900 h-1 rounded-full transition-all duration-300 ease-out"
          [style.width.%]="(currentStep / steps.length) * 100"
        ></div>
      </div>

      <!-- Step Labels -->
      <div class="flex justify-between text-xs text-gray-500 font-medium">
        @for (step of steps; track step.id) {
          <span
            class="transition-colors duration-200"
            [class.text-gray-900]="step.id <= currentStep"
          >
            {{ step.title }}
          </span>
        }
      </div>
    </div>

    <!-- Modal Body -->
    <div class="flex-1 overflow-y-auto">
      <!-- Loading State -->
      @if (loading) {
        <div class="flex items-center justify-center py-16">
          <div class="text-center">
            <div class="animate-spin rounded-full h-6 w-6 border-2 border-gray-200 border-t-gray-900 mx-auto mb-3"></div>
            <p class="text-gray-500 text-sm font-light">Loading playlists...</p>
          </div>
        </div>
      } @else {
        <!-- Step Content -->
        <div class="px-8 py-6">
            @switch (currentStep) {
              @case (1) {
                <app-step-playlist-selection
                  [availablePlaylists]="availablePlaylists"
                  [selectedPlaylistId]="formData.playlist_id"
                  (playlistSelected)="onPlaylistSelected($event)"
                />
              }
              @case (2) {
                <app-step-time-configuration
                  [startTime]="formData.start_time"
                  [endTime]="formData.end_time"
                  [priority]="formData.priority"
                  [priorityOptions]="PRIORITY_OPTIONS"
                  (timeConfigured)="onTimeConfigured($event)"
                />
              }
              @case (3) {
                <app-step-days-selection
                  [availableDays]="DAYS_OF_WEEK"
                  [selectedDays]="formData.days_of_week"
                  (daysSelected)="onDaysSelected($event)"
                />
              }
              @case (4) {
                <app-step-review-submit
                  [formData]="formData"
                  [availablePlaylists]="availablePlaylists"
                  [priorityOptions]="PRIORITY_OPTIONS"
                  [submitting]="submitting"
                />
              }
            }
        </div>
      }
    </div>

    <!-- Modal Footer -->
    <div class="px-8 py-6 border-t border-gray-100">
      <div class="flex items-center justify-between">
        <!-- Previous Button -->
        @if (canGoPrevious()) {
          <button
            type="button"
            (click)="goToPrevious()"
            [disabled]="submitting"
            class="px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm font-medium"
          >
            <span class="material-icons mr-2 text-base">arrow_back</span>
            Back
          </button>
        } @else {
          <div></div>
        }

        <!-- Action Buttons -->
        <div class="flex items-center space-x-3">
          <!-- Cancel Button -->
          <button
            type="button"
            (click)="handleCancel()"
            [disabled]="submitting"
            class="px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
          >
            Cancel
          </button>

          <!-- Next/Submit Button -->
          @if (currentStep < steps.length) {
            <button
              type="button"
              (click)="goToNext()"
              [disabled]="submitting || !canGoNext()"
              class="px-6 py-2 bg-gray-900 text-white rounded-xl hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm font-medium transition-all duration-200"
            >
              Continue
              <span class="material-icons ml-2 text-base">arrow_forward</span>
            </button>
          } @else {
            <button
              type="button"
              (click)="handleSubmit()"
[disabled]="submitting"
              class="px-6 py-2 bg-gray-900 text-white rounded-xl hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm font-medium transition-all duration-200"
            >
              @if (submitting) {
                <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                {{ 'Creating...' }}
              } @else {
                Create Schedule
              }
            </button>
          }
        </div>
      </div>
    </div>
  </div>
</div>
