import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval, from } from 'rxjs';
import { switchMap, map, catchError, startWith } from 'rxjs/operators';
import { supabase } from './supabase.config';

@Injectable({
  providedIn: 'root'
})
export class ConnectivityService {
  private onlineStatusSubject = new BehaviorSubject<boolean>(navigator.onLine);
  private heartbeatStatusSubject = new BehaviorSubject<boolean>(false);
  
  public onlineStatus$ = this.onlineStatusSubject.asObservable();
  public heartbeatStatus$ = this.heartbeatStatusSubject.asObservable();
  public isOnline$ = new BehaviorSubject<boolean>(navigator.onLine && false); // Initially false until heartbeat confirms

  constructor() {
    // Listen to browser's online/offline events
    window.addEventListener('online', () => {
      this.onlineStatusSubject.next(true);
      this.checkHeartbeat();
    });

    window.addEventListener('offline', () => {
      this.onlineStatusSubject.next(false);
      this.heartbeatStatusSubject.next(false);
      this.isOnline$.next(false);
    });

    // Periodically check heartbeat when online
    interval(30000) // Check every 30 seconds
      .pipe(
        switchMap(() => this.checkHeartbeat()),
        catchError(() => {
          this.heartbeatStatusSubject.next(false);
          this.isOnline$.next(false);
          return from([false]);
        })
      )
      .subscribe();

    // Initial check
    this.checkHeartbeat();
  }

  /**
   * Check if we can connect to the backend
   */
  private async checkHeartbeat(): Promise<boolean> {
    if (!navigator.onLine) {
      this.heartbeatStatusSubject.next(false);
      this.isOnline$.next(false);
      return false;
    }

    try {
      // Simple heartbeat check - try to get a basic response from Supabase
      const { data, error } = await supabase
        .from('playlists')
        .select('id')
        .limit(1);

      const isOnline = !error;
      this.heartbeatStatusSubject.next(isOnline);
      this.isOnline$.next(isOnline);
      
      return isOnline;
    } catch (error) {
      this.heartbeatStatusSubject.next(false);
      this.isOnline$.next(false);
      return false;
    }
  }

  /**
   * Public method to manually trigger a connectivity check
   */
  public async checkConnectivity(): Promise<boolean> {
    const isOnline = await this.checkHeartbeat();
    return isOnline;
  }
}