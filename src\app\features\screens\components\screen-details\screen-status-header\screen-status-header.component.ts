import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Screen } from '../../../../../models/screen.model';
import { PlaylistService } from '../../../../playlists/services/playlist.service';
import { Playlist } from '../../../../../models/playlist.model';

@Component({
  selector: 'app-screen-status-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <header class="bg-white border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <button
              routerLink="/screens"
              class="text-gray-400 hover:text-gray-600 p-2"
            >
              <span class="material-icons">arrow_back</span>
            </button>
            <h1 class="ml-4 text-xl font-medium text-gray-900">
              {{ screen?.name }}
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Heartbeat Status -->
            <div class="flex items-center gap-2">
              <div
                [class]="'h-2.5 w-2.5 rounded-full ' + getHeartbeatColor()"
              ></div>
              <span class="text-sm font-medium">{{ getHeartbeatText() }}</span>
            </div>
            
            <!-- Original Status (hidden for now) -->
            <!--
            <div class="flex items-center gap-2">
              <div
                [class]="'h-2.5 w-2.5 rounded-full ' + getStatusColor()"
              ></div>
              <span class="text-sm font-medium">{{ screen?.status }}</span>
            </div>
            -->
          </div>
        </div>
      </div>
    </header>
  `,
})
export class ScreenStatusHeaderComponent implements OnInit {
  @Input() screen: Screen | null = null;
  currentPlaylist: Playlist | null = null;

  constructor(private playlistService: PlaylistService) {}

  ngOnInit() {
    this.loadCurrentPlaylist();
  }

  loadCurrentPlaylist() {
    if (this.screen?.current_playlist) {
      this.playlistService.getPlaylist(this.screen.current_playlist).subscribe({
        next: (playlist) => {
          this.currentPlaylist = playlist;
        },
        error: (error) => {
          console.error('Error loading playlist:', error);
        }
      });
    }
  }

  getHeartbeatText(): string {
    if (!this.screen) return 'Unknown';
    
    // Check if screen is offline based on last ping (more than 5 minutes ago)
    if (this.isScreenOffline()) {
      return 'Offline';
    }
    
    // If online, show current playlist or "No Playlist"
    if (this.currentPlaylist) {
      return this.currentPlaylist.name;
    } else if (this.screen.current_playlist) {
      return 'Loading...';
    } else {
      return 'No Playlist';
    }
  }

  getHeartbeatColor(): string {
    if (!this.screen) return 'bg-gray-400';
    
    // Check if screen is offline based on last ping (more than 5 minutes ago)
    if (this.isScreenOffline()) {
      return 'bg-gray-400'; // Gray for offline
    }
    
    // Green for online with active playlist, yellow for online without playlist
    return this.currentPlaylist ? 'bg-green-500' : 'bg-yellow-500';
  }

  isScreenOffline(): boolean {
    if (!this.screen || !this.screen.last_ping) return true;
    
    // Parse the last_ping date
    const lastPing = new Date(this.screen.last_ping);
    const now = new Date();
    
    // Check if lastPing is a valid date
    if (isNaN(lastPing.getTime())) return true;
    
    // Calculate the difference in minutes
    const diffInMinutes = (now.getTime() - lastPing.getTime()) / (1000 * 60);
    
    // Consider offline if last ping was more than 5 minutes ago
    return diffInMinutes > 5;
  }

  getStatusColor(): string {
    if (!this.screen) return 'bg-gray-400';

    switch (this.screen.status) {
      case 'online':
        return 'bg-green-500';
      case 'offline':
        return 'bg-gray-400';
      case 'maintenance':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  }
}