export interface ScheduleFormData {
  playlist_id: string;
  playlist_name?: string;
  start_time: string;
  end_time: string;
  priority: number;
  days_of_week: string[];
}

export interface ScheduleFormStep {
  id: number;
  title: string;
  description: string;
  isValid: boolean;
  isCompleted: boolean;
}

export interface ScheduleFormValidation {
  playlist_id: {
    required: boolean;
    message: string;
  };
  start_time: {
    required: boolean;
    message: string;
  };
  end_time: {
    required: boolean;
    format: boolean;
    timeRange: boolean;
    message: string;
  };
  priority: {
    required: boolean;
    range: boolean;
    message: string;
  };
  days_of_week: {
    required: boolean;
    minSelection: boolean;
    message: string;
  };
}

export interface ScheduleFormErrors {
  playlist_id?: string;
  start_time?: string;
  end_time?: string;
  priority?: string;
  days_of_week?: string;
  general?: string;
  time_conflict?: string;
  duration?: string;
}

export const SCHEDULE_FORM_STEPS: ScheduleFormStep[] = [
  {
    id: 1,
    title: 'Playlist',
    description: 'Choose what to display',
    isValid: false,
    isCompleted: false
  },
  {
    id: 2,
    title: 'Time',
    description: 'Set when to display',
    isValid: false,
    isCompleted: false
  },
  {
    id: 3,
    title: 'Days',
    description: 'Select which days',
    isValid: false,
    isCompleted: false
  },
  {
    id: 4,
    title: 'Review',
    description: 'Confirm and create',
    isValid: false,
    isCompleted: false
  }
];

export const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday', 
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

export const PRIORITY_OPTIONS = [
  { value: 1, label: 'High Priority', description: 'Overrides other schedules' },
  { value: 2, label: 'Normal Priority', description: 'Standard scheduling priority' },
  { value: 3, label: 'Low Priority', description: 'Runs when no higher priority schedules are active' }
];
