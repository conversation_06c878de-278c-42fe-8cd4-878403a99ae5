// src/app/features/final/final-playlist-preview.component.ts
import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { PlaylistItem } from '../../models/playlist.model';
import { supabase } from '../../core/services/supabase.config';
import { ConnectivityService } from '../../core/services/connectivity.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-final-playlist-preview',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="fixed inset-0 bg-black flex items-center justify-center">
      <!-- Loading -->
      <div *ngIf="loading" class="text-white text-xl">Loading playlist...</div>

      <!-- Error -->
      <div *ngIf="error && !isOffline" class="text-white text-xl">{{ error }}</div>
      
      <!-- Offline Display -->
      <div *ngIf="isOffline" class="text-white text-xl text-center p-8">
        <div class="mb-4">
          <div class="inline-block animate-pulse">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
            </svg>
          </div>
        </div>
        <h2 class="text-2xl font-bold mb-2">OFFLINE MODE</h2>
        <p class="mb-4">Unable to connect to server. Displaying cached content.</p>
        <div class="flex items-center justify-center space-x-2">
          <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          <span>Heartbeat Lost</span>
        </div>
      </div>

      <!-- Loading message for between ads -->
      <div *ngIf="!loading && !error && !isOffline && displayItems.length === 0" class="text-white text-center">
        <h2 class="text-2xl mb-4">Loading next advertisement...</h2>
        <p class="text-lg">Please wait.</p>
      </div>

      <!-- Content -->
      <div *ngIf="!loading && !error && !isOffline && displayItems.length > 0" class="w-full h-full relative" [class.tv-display-content]="true" [class.portrait-mode]="isPortraitMode">
        <!-- Display the current image/content -->
        <div *ngFor="let item of displayItems; let i = index" class="absolute inset-0">
          <!-- Image -->
          <img
            *ngIf="item.type === 'image'"
            [src]="item.content.url"
            [style.display]="currentIndex === i ? 'block' : 'none'"
            [class]="isPortraitMode ? 'portrait-media-item' : 'w-full h-full object-contain'"
          />

          <!-- Video -->
          <video
            #videoPlayer
            *ngIf="item.type === 'video'"
            [src]="item.content.url"
            [style.display]="currentIndex === i ? 'block' : 'none'"
            [class]="isPortraitMode ? 'portrait-media-item' : 'w-full h-full object-contain'"
            [muted]="item.settings.muted ?? true"
            [loop]="false"
            (ended)="handleVideoEnded()"
          ></video>
        </div>

        <!-- Playlist Info Overlay -->
        <div class="absolute top-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg">
          <div class="text-lg font-semibold">{{ playlistName }}</div>
          <div *ngIf="screenStatus === 'offline'" class="text-red-400 font-medium">Offline</div>
        </div>
        
        <!-- Heartbeat indicator -->
        <div class="absolute top-4 right-4 z-10">
          <div class="flex items-center bg-black bg-opacity-50 rounded-full px-3 py-1">
            <div class="w-3 h-3 rounded-full mr-2" 
                 [ngClass]="isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'"></div>
            <span class="text-white text-sm">{{ isConnected ? 'ONLINE' : 'OFFLINE' }}</span>
          </div>
        </div>

        <!-- Progress bar at bottom -->
        <div class="absolute bottom-0 left-0 right-0 h-1 bg-gray-800">
          <div
            class="h-full bg-blue-500 transition-all"
            [style.width.%]="progressPercentage"
          ></div>
        </div>
      </div>
    </div>
  `
})
export class FinalPlaylistPreviewComponent implements OnInit, OnDestroy {
  @ViewChild('videoPlayer') videoPlayer?: ElementRef<HTMLVideoElement>;

  loading = true;
  error: string | null = null;
  items: PlaylistItem[] = []; // Original playlist items
  displayItems: PlaylistItem[] = []; // Currently displayed item
  currentIndex = 0;
  isPortraitMode = false;
  playlistName = 'Loading...';
  screenStatus: 'online' | 'offline' | 'maintenance' | 'error' = 'online';
  isOffline = false;
  isConnected = true;

  // Fixed duration for all ads (12 seconds)
  private readonly FIXED_DURATION = 12; // in seconds

  // Timer variables
  private slideTimer: any = null;
  private startTime: number = 0;
  private currentDuration: number = 0;
  progressPercentage: number = 0;
  private animationFrameId: number | null = null;

  // Subscription to URL changes
  private routeSubscription: Subscription;
  private connectivitySubscription: Subscription | null = null;

  constructor(
    private route: ActivatedRoute,
    private connectivityService: ConnectivityService
  ) {
    // Subscribe to URL changes to detect when the page is reloaded
    this.routeSubscription = this.route.paramMap.subscribe(() => {
      // If we already have items loaded, show the next ad
      if (this.items.length > 0 && !this.loading) {
        this.showNextAd();
      }
    });
  }

  ngOnInit() {
    this.loadPlaylist();
    this.checkScreenOrientation();
    this.setupConnectivityListener();
  }

  private setupConnectivityListener() {
    this.connectivitySubscription = this.connectivityService.isOnline$.subscribe(isOnline => {
      this.isConnected = isOnline;
      this.isOffline = !isOnline && !this.loading;
      
      // If we were offline and now we're back online, reload the playlist
      if (!this.isOffline && isOnline && this.items.length === 0) {
        this.loadPlaylist();
      }
    });
  }

  private checkScreenOrientation() {
    // Check URL params for screen orientation data
    const screenId = this.route.snapshot.queryParamMap.get('screenId');
    if (screenId) {
      this.loadScreenOrientation(screenId);
    }
  }

  private async loadScreenOrientation(screenId: string) {
    try {
      const { data: screen, error } = await supabase
        .from('screens')
        .select('settings,status')
        .eq('id', screenId)
        .single();
      
      if (!error && screen) {
        // Update screen status
        this.screenStatus = screen.status;
        
        // Update orientation if settings exist
        if (screen?.settings?.screen_rotation) {
          this.isPortraitMode = screen.settings.screen_rotation === 90 || screen.settings.screen_rotation === 270;
          console.log('Screen orientation loaded:', this.isPortraitMode ? 'portrait' : 'landscape');
        }
      }
    } catch (error) {
      console.error('Error loading screen orientation:', error);
    }
  }

  ngOnDestroy() {
    this.clearTimers();

    // Unsubscribe from route events to prevent memory leaks
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    
    if (this.connectivitySubscription) {
      this.connectivitySubscription.unsubscribe();
    }
  }

  private clearTimers() {
    if (this.slideTimer) {
      clearTimeout(this.slideTimer);
      this.slideTimer = null;
    }

    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * Show the next ad in the playlist
   * This is called when the URL is loaded or reloaded
   */
  showNextAd() {
    if (this.items.length === 0) {
      console.log('No more ads to display');
      return;
    }

    // Take the first item from the original playlist
    const nextItem = this.items.shift();
    if (nextItem) {
      // Set fixed duration for all ads
      nextItem.duration = this.FIXED_DURATION;

      // Add to display items
      this.displayItems = [nextItem];
      this.currentIndex = 0;

      // Save this item as shown in localStorage
      const playlistId = this.route.snapshot.paramMap.get('id');
      if (playlistId) {
        const storageKey = `playlist_${playlistId}_shown_items`;
        const shownItemIds = JSON.parse(localStorage.getItem(storageKey) || '[]');
        shownItemIds.push(nextItem.id);
        localStorage.setItem(storageKey, JSON.stringify(shownItemIds));
        console.log(`Saved item ${nextItem.id} as shown in localStorage`);
      }

      console.log(`Showing ad: ${nextItem.name} for ${this.FIXED_DURATION} seconds`);
      this.showCurrentItem();
    }
  }

  async loadPlaylist() {
    try {
      const playlistId = this.route.snapshot.paramMap.get('id');
      console.log('Loading playlist:', playlistId);

      if (!playlistId) {
        throw new Error('No playlist ID provided');
      }

      // First, get the playlist name
      const { data: playlistData, error: playlistError } = await supabase
        .from('playlists')
        .select('name')
        .eq('id', playlistId)
        .single();

      if (playlistError) throw playlistError;
      
      this.playlistName = playlistData.name;
      console.log('Playlist name:', this.playlistName);

      // Check screen status if screenId is provided
      const screenId = this.route.snapshot.queryParamMap.get('screenId');
      if (screenId) {
        const { data: screenData, error: screenError } = await supabase
          .from('screens')
          .select('status')
          .eq('id', screenId)
          .single();

        if (!screenError && screenData) {
          this.screenStatus = screenData.status;
          console.log('Screen status:', this.screenStatus);
        }
      }

      const { data, error } = await supabase
        .from('playlists')
        .select(`
          *,
          items:playlist_items(*)
        `)
        .eq('id', playlistId)
        .single();

      if (error) throw error;

      console.log('Playlist data loaded:', data);

      // Map the items
      const allItems = (data.items || []).map((item: any) => ({
        id: item.id,
        type: item.type || 'image',
        name: item.name || '',
        duration: item.duration || 10,
        content: {
          url: item.content_url || '',
          thumbnail: item.thumbnail_url || ''
        },
        settings: {
          transition: item.transition || 'fade',
          transitionDuration: item.transition_duration || 0.5,
          scaling: item.scaling || 'fit',
          muted: item.muted || true,
          loop: item.loop || false
        },
        schedule: null
      }));

      console.log('Mapped items:', allItems);

      if (allItems.length === 0) {
        throw new Error('Playlist has no items');
      }

      // Check localStorage for already shown items
      const storageKey = `playlist_${playlistId}_shown_items`;
      const shownItemIds = JSON.parse(localStorage.getItem(storageKey) || '[]');

      console.log('Already shown items:', shownItemIds);

      // Filter out already shown items
      this.items = allItems.filter((item: PlaylistItem) => !shownItemIds.includes(item.id));

      // If all items have been shown, reset and start over
      if (this.items.length === 0 && allItems.length > 0) {
        console.log('All items have been shown, starting over from the beginning');
        localStorage.removeItem(storageKey);
        this.items = [...allItems]; // Create a copy of all items
      }

      console.log('Items to show:', this.items);

      this.loading = false;

      // Show the next ad immediately
      this.showNextAd();

    } catch (err: any) {
      console.error('Error loading playlist:', err);
      this.error = err.message || 'Failed to load playlist';
      this.loading = false;
      
      // If we're offline, show offline mode instead of error
      if (!this.connectivityService.isOnline$.value) {
        this.isOffline = true;
      }
    }
  }

  showCurrentItem() {
    // Clear any existing timers
    this.clearTimers();

    if (this.displayItems.length === 0) {
      return;
    }

    const currentItem = this.displayItems[this.currentIndex];
    console.log(`Showing item ${this.currentIndex + 1}/${this.displayItems.length}: ${currentItem.name} (${currentItem.duration}s)`);

    if (currentItem.type === 'video') {
      // For videos, wait for the video to end (handling through the ended event)
      setTimeout(() => {
        if (this.videoPlayer?.nativeElement) {
          this.videoPlayer.nativeElement.currentTime = 0;
          this.videoPlayer.nativeElement.play().catch(err => {
            console.error('Error playing video:', err);
            // If video fails to play, move to next slide after the defined duration
            this.startTimerForCurrentItem();
          });
        } else {
          // If video player is not available for some reason, fall back to timer
          this.startTimerForCurrentItem();
        }
      }, 0);
    } else {
      // For images and other content, use the fixed duration
      this.startTimerForCurrentItem();
    }
  }

  startTimerForCurrentItem() {
    const currentItem = this.displayItems[this.currentIndex];
    this.currentDuration = currentItem.duration * 1000; // Convert to milliseconds
    this.startTime = Date.now();

    // Set up the timer to move to the next item
    this.slideTimer = setTimeout(() => {
      this.adFinished();
    }, this.currentDuration);

    // Start progress bar animation
    this.updateProgressBar();
  }

  updateProgressBar() {
    const elapsed = Date.now() - this.startTime;
    this.progressPercentage = Math.min((elapsed / this.currentDuration) * 100, 100);

    if (this.progressPercentage < 100) {
      this.animationFrameId = requestAnimationFrame(() => this.updateProgressBar());
    }
  }

  handleVideoEnded() {
    console.log('Video ended naturally');
    this.adFinished();
  }

  // Called when an ad finishes playing
  adFinished() {
    this.clearTimers();
    this.progressPercentage = 0;

    // Remove the displayed ad
    this.displayItems = [];

    console.log('Ad finished, waiting for next URL call');
  }

  /**
   * Reset the playlist to show all ads again
   * This can be called manually if needed
   */
  resetPlaylist() {
    const playlistId = this.route.snapshot.paramMap.get('id');
    if (playlistId) {
      const storageKey = `playlist_${playlistId}_shown_items`;
      localStorage.removeItem(storageKey);
      console.log('Playlist reset, all ads will be shown again');

      // Reload the playlist
      this.loadPlaylist();
    }
  }
}