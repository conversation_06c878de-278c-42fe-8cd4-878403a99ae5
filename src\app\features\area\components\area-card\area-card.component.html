<!-- area-card.component.html -->
<div class="group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden"
     (dragover)="onDragOver($event)"
     (dragleave)="onDragLeave($event)"
     (drop)="onDrop($event)"
     [class.bg-blue-50]="isDragOver"
     [class.border-2]="isDragOver"
     [class.border-dashed]="isDragOver"
     [class.border-blue-300]="isDragOver">
  <!-- Main Card Section - Clickable Area -->
  <div class="p-4 cursor-pointer" (click)="toggleScreens($event)">
    <div class="flex items-start justify-between">
      <div class="flex-1 min-w-0">
        <!-- Header with Status -->
        <div class="flex items-center justify-between mb-1">
          <h3 class="text-base font-semibold text-gray-900 truncate pr-6">
            {{ area.name }}
          </h3>
          <div class="flex items-center space-x-2 ml-2">
            <!-- Status Indicator -->
            <div
              class="w-2 h-2 rounded-full flex-shrink-0"
              [class]="area.status === 'active' ? 'bg-green-500' : 'bg-gray-300'"
              [title]="area.status === 'active' ? 'Online' : 'Offline'"
            ></div>
            <!-- Toggle Indicator -->
            <span class="material-icons text-gray-400 text-xl">
              {{ showScreens ? 'expand_less' : 'expand_more' }}
            </span>
          </div>
        </div>

        <!-- Location -->
        <p class="text-sm text-gray-600 mb-2 flex items-center">
          <span class="material-icons text-gray-400 text-base mr-1">location_on</span>
          {{ area.location || 'No location set' }}
        </p>

        <!-- Stats -->
        <div class="flex items-center justify-between mt-3">
          <div class="flex items-center text-sm">
            <span class="material-icons text-gray-400 text-base mr-1">devices</span>
            <span>
              <span class="font-medium text-gray-900">{{ area.stats.onlineScreens }}</span>
              <span class="text-gray-500">/{{ area.stats.totalScreens }} online</span>
            </span>
          </div>
          @if (area.stats.activePlaylist) {
            <div class="flex items-center text-sm text-gray-600 bg-gray-50 px-2 py-1 rounded">
              <span class="material-icons text-gray-400 text-base mr-1">playlist_play</span>
              <span class="truncate max-w-[120px]" [title]="area.stats.activePlaylist">
                {{ area.stats.activePlaylist }}
              </span>
            </div>
          }
        </div>
      </div>

      <!-- Menu Button -->
      <button 
        (click)="toggleMenu($event)"
        class="ml-2 p-1 rounded-full hover:bg-gray-100 transition-colors">
        <span class="material-icons text-gray-500">more_vert</span>
      </button>
    </div>
  </div>

  <!-- Dropdown Menu -->
  @if (showMenu) {
    <div class="absolute right-4 top-12 z-10 bg-white rounded-lg shadow-lg border border-gray-200 py-1 w-48">
      <button 
        (click)="onEdit($event)"
        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
        <span class="material-icons text-sm mr-2">edit</span>
        Edit Area
      </button>
      <button 
        (click)="onMoveArea($event)"
        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
        <span class="material-icons text-sm mr-2">place</span>
        Move Area
      </button>
      <button 
        (click)="onToggleStatus($event)"
        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
        <span class="material-icons text-sm mr-2">
          {{ area.status === 'active' ? 'pause' : 'play_arrow' }}
        </span>
        {{ area.status === 'active' ? 'Deactivate' : 'Activate' }}
      </button>
      <hr class="my-1">
      <button 
        (click)="onDelete($event)"
        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center">
        <span class="material-icons text-sm mr-2">delete</span>
        Delete Area
      </button>
    </div>
  }

  <!-- Move Area Dropdown -->
  @if (showMoveDropdown) {
    <div class="absolute right-4 top-12 z-10 bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-64">
      <h3 class="text-sm font-medium text-gray-900 mb-3">Move Area to New Location</h3>
      <div class="mb-3">
        <label class="block text-xs text-gray-500 mb-1">Current Location</label>
        <div class="text-sm text-gray-900 bg-gray-50 p-2 rounded">{{ area.location }}</div>
      </div>
      <div class="mb-4">
        <label class="block text-xs text-gray-500 mb-1">New Location</label>
        <select 
          [(ngModel)]="newLocation"
          class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-200">
          @for (location of locations; track location) {
            <option [value]="location">{{ location }}</option>
          }
        </select>
        <div class="mt-2 flex items-center text-xs text-gray-500">
          <span class="material-icons text-xs mr-1">info</span>
          Or type a custom location
        </div>
      </div>
      <div class="flex justify-end space-x-2">
        <button 
          (click)="cancelMove()"
          class="px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded-lg">
          Cancel
        </button>
        <button 
          (click)="confirmMove()"
          [disabled]="newLocation === area.location"
          class="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
          Move
        </button>
      </div>
    </div>
  }

  <!-- Screens Dropdown -->
  @if (showScreens) {
    <div class="border-t border-gray-100 bg-gray-50 transition-all duration-200">
      @if (area.screens && area.screens.length > 0) {
        <div class="p-4">
          <h4 class="text-sm font-medium text-gray-700 mb-3 px-1">Screens in this area</h4>
          <div class="space-y-2">
            @for (screen of area.screens; track screen.id) {
              <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100 shadow-xs hover:shadow-sm transition-shadow cursor-pointer"
                   (click)="onScreenClick(screen)"
                   draggable="true"
                   (dragstart)="onDragStart($event, screen)">
                <!-- Playlist Visualization (Left Side) -->
                <div class="flex items-center flex-shrink-0 w-24 h-16 mr-3 relative"
                     (mouseenter)="onScreenHover(screen.id, $event)"
                     (mouseleave)="onScreenLeave()">
                  @if (screen.current_playlist && playlists[screen.id]) {
                    <app-tv-display
                      [currentPlaylist]="playlists[screen.id]"
                      [isPlaying]="true"
                      [screenOrientation]="getScreenOrientation(screen)"
                      [compact]="true"
                      class="w-full h-full"
                    />
                  } @else if (screen.current_playlist) {
                    <div class="w-full h-full bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                      <span class="material-icons text-gray-400 text-sm">hourglass_empty</span>
                    </div>
                  } @else {
                    <div class="w-full h-full bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                      <span class="material-icons text-gray-400 text-sm">playlist_add</span>
                    </div>
                  }
                </div>
                
                <div class="flex items-center min-w-0 flex-1">
                  <div class="flex items-center min-w-0 flex-1">
                    <div class="w-3 h-3 rounded-full flex-shrink-0 mr-3 mt-0.5 flex items-center justify-center"
                         [class]="getScreenStatusClass(screen) + (screen.status === 'online' && !isScreenOffline(screen) ? ' animate-pulse' : '')"
                         [title]="getScreenStatusText(screen)">
                    </div>
                    <div class="min-w-0 flex-1">
                      <div class="font-medium text-gray-900 truncate">{{ screen.name }}</div>
                      <div class="text-xs text-gray-500 mt-0.5 flex flex-wrap items-center gap-1">
                        <span>{{ screen.resolution }}</span>
                        <span>•</span>
                        <span class="capitalize">{{ screen.orientation || 'N/A' }}</span>
                      </div>
                    </div>
                  </div>
                  
                  @if (screen.channel_name) {
                    <span class="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full whitespace-nowrap ml-2 flex-shrink-0">
                      {{ screen.channel_name }}
                    </span>
                  }
                </div>
              </div>
            }
          </div>
        </div>
      } @else {
        <div class="p-6 text-center">
          <div class="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
            <span class="material-icons text-gray-400">tv_off</span>
          </div>
          <p class="text-gray-500 text-sm">No screens assigned yet</p>
          <p class="text-gray-400 text-xs mt-1">Add screens to this area to get started</p>
        </div>
      }
    </div>
  }
  
  <!-- Hover Modal for Playlist Preview -->
  @if (hoveredScreenId && getHoveredPlaylist()) {
    <div class="fixed inset-0 z-50 pointer-events-none">
      <div class="absolute bg-white rounded-xl shadow-2xl border border-gray-200 p-4 w-full max-w-lg pointer-events-auto"
           [style.left.px]="getModalLeft()"
           [style.top.px]="getModalTop()"
           (mouseleave)="onScreenLeave()">
        <div class="flex justify-between items-start mb-3">
          <h3 class="font-semibold text-gray-900">
            {{ getHoveredPlaylist()?.name }}
          </h3>
          <button (click)="hoveredScreenId = null" class="text-gray-400 hover:text-gray-600">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="h-64">
          @if (getHoveredPlaylist()) {
            <app-tv-display
              [currentPlaylist]="getHoveredPlaylist()!"
              [isPlaying]="true"
              [screenOrientation]="'landscape'"
              [compact]="false"
              class="w-full h-full"
            />
          }
        </div>
        <div class="mt-3 text-sm text-gray-600">
          <div class="flex justify-between">
            <span>Items: {{ getHoveredPlaylist()?.items?.length || 0 }}</span>
            <span>Duration: {{ getPlaylistDuration(getHoveredPlaylist()) }}s</span>
          </div>
        </div>
      </div>
    </div>
  }
</div>