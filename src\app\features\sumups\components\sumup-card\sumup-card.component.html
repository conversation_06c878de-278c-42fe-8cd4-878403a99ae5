<!-- sumup-card.component.html -->
<div class="group relative bg-white rounded-xl border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
    <!-- Main Card Section - Clickable Area -->
    <a [routerLink]="['/sumups', sumup.id]" class="block p-5 cursor-pointer">
      <!-- Header with Status -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <div
            class="w-2 h-2 rounded-full"
            [class]="getStatusColor(sumup.status)"
            [title]="sumup.status"
          ></div>
          <h3 class="text-lg font-medium text-gray-900">{{ sumup.name }}</h3>
        </div>
  
        <!-- Actions Dropdown -->
        <div class="relative">
          <button
            (click)="toggleMenu($event)"
            class="p-1.5 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-50"
            title="More options"
          >
            <span class="material-icons text-lg">more_vert</span>
          </button>
  
          <!-- Dropdown Menu -->
          @if (showMenu) {
            <div 
              class="absolute right-0 mt-1 w-48 rounded-lg bg-white shadow-lg border border-gray-100 py-1 z-10"
            >
              <button
                (click)="onEdit($event)"
                class="w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center"
              >
                <span class="material-icons text-lg mr-2 text-gray-400">edit</span>
                Edit Sumup
              </button>
              <button
                (click)="onToggleStatus($event)"
                class="w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center"
              >
                <span class="material-icons text-lg mr-2 text-gray-400">
                  {{ sumup.status === 'active' ? 'pause_circle' : 'play_circle' }}
                </span>
                {{ sumup.status === 'active' ? 'Deactivate' : 'Activate' }}
              </button>
              <button
                (click)="onDelete($event)"
                class="w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
              >
                <span class="material-icons text-lg mr-2">delete</span>
                Delete Sumup
              </button>
            </div>
          }
        </div>
      </div>
  
      <!-- Description -->
      @if (sumup.description) {
        <p class="text-sm text-gray-500 mb-4 line-clamp-2">{{ sumup.description }}</p>
      } @else {
        <p class="text-sm text-gray-400 italic mb-4">No description</p>
      }
  
      <!-- Stats -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span class="material-icons text-gray-400 mr-1">playlist_play</span>
          <span class="text-sm">
            <span class="font-medium">{{ getPlaylistCount() }}</span>
            <span class="text-gray-500"> playlists</span>
          </span>
        </div>
        <div class="text-sm text-gray-500">
          Updated {{ sumup.updated_at | date:'MMM d, y' }}
        </div>
      </div>
    </a>
  </div>