// src/app/features/screens/components/screen-card/screen-card.component.ts

import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule, SlicePipe } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Screen } from '../../../../models/screen.model';
import { PlaylistService } from '../../../playlists/services/playlist.service';
import { Playlist, PlaylistItem } from '../../../../models/playlist.model';
import { TvDisplayComponent } from '../../../../shared/components/tv-display/tv-display.component';

interface PlaylistMap {
  [key: string]: Playlist | null;
}

@Component({
  selector: 'app-screen-card',
  standalone: true,
  imports: [CommonModule, RouterModule, SlicePipe, TvDisplayComponent],
  template: `
    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden group cursor-pointer"
         [class]="getCardContainerClass()"
         (click)="handleViewDetails($event)">
      
      <!-- Screen Visual Representation -->
      <div class="relative p-3 my-4">
        <!-- Status Indicator on Screen -->
        <div [class]="'absolute top-1 left-1 w-1.5 h-1.5 rounded-full ' + getStatusColor()"></div>
        
        <!-- Playlist or Status Display -->
        <div class="text-center py-1 pr-1 pl-0 w-full">
          @if (isScreenOffline()) {
            @if (screen.current_playlist) {
              <div class="text-gray-500 text-[0.6rem]">Playlist (Offline)</div>
            } @else {
              <div class="text-red-500 font-medium text-xs">OFFLINE</div>
            }
          } @else if (screen.status === 'error') {
            @if (screen.current_playlist) {
              <div class="text-red-500 text-[0.6rem]">Playlist (Error)</div>
            } @else {
              <div class="text-red-500 font-medium text-xs">ERROR</div>
            }
          } @else if (screen.status === 'maintenance') {
            @if (screen.current_playlist) {
              <div class="text-yellow-500 text-[0.6rem]">Playlist (Maint)</div>
            } @else {
              <div class="text-yellow-500 font-medium text-xs">MAINT</div>
            }
          } @else if (screen.current_playlist && playlists[screen.id]) {
            <!-- Show TV display preview -->
            <app-tv-display
              [currentPlaylist]="playlists[screen.id]"
              [isPlaying]="true"
              [screenOrientation]="getScreenOrientation()"
              [compact]="true"
              style="width: 100%; height: 100%;"
            />
          } @else if (screen.current_playlist && currentPlaylistName) {
            <div class="text-gray-700 text-[0.6rem] truncate w-full px-1">{{ currentPlaylistName }}</div>
          } @else if (screen.current_playlist) {
            <div class="text-blue-600 text-[0.6rem]">Loading...</div>
          } @else {
            <div class="text-gray-500 text-[0.6rem]">No Playlist</div>
          }
        </div>
        
        <!-- Resolution Badge -->
        <div class="absolute bottom-0.5 right-0.5 bg-white/80 backdrop-blur-sm text-[0.5rem] px-1 py-0.5 rounded text-gray-600 font-mono">
          {{ screen.resolution || 'N/A' }}
        </div>
        
        <!-- Menu Button -->
        <button
          (click)="toggleExpand($event)"
          class="absolute top-1.5 right-1.5 bg-white/90 hover:bg-white rounded-full p-1 shadow-sm transition-all duration-200 hover:shadow group-hover:opacity-100 opacity-0"
        >
          <span class="material-icons text-[0.7rem] text-gray-600">
            {{ isExpanded ? 'expand_less' : 'more_horiz' }}
          </span>
        </button>
      </div>
      
      <!-- Card Information -->
      <div class="px-3 pb-3">
        <!-- Screen Name & Status -->
        <div class="flex items-start justify-between mb-2">
          <div class="flex-1">
            <h3 class="font-semibold text-gray-900 text-sm mb-1 truncate">
              {{ screen.name }}
            </h3>
            <div class="flex items-center gap-1">
              <span [class]="getStatusBadgeClass()">{{ screen.status }}</span>
            </div>
          </div>
        </div>
        
        <!-- Quick Info -->
        <div class="space-y-1 text-xs text-gray-600">
          <!-- Last Ping Info -->
          <div class="flex items-center">
            <span class="material-icons text-[0.6rem] mr-1">schedule</span>
            <span class="truncate">Last: {{ formatDate(screen.last_ping) }}</span>
          </div>
          
          <!-- Channel Info -->
          <div class="flex items-center">
            <span class="material-icons text-[0.6rem] mr-1">settings_input_hdmi</span>
            <span class="truncate">{{ screen.channel_name }}</span>
          </div>
        </div>
      </div>

      <!-- Expandable Section -->
      @if (isExpanded) {
        <div class="border-t border-gray-100 bg-gray-50 p-3 space-y-2">
          <!-- Detailed Stats -->
          <div [class]="getStatsGridClass()">
            <div class="text-center">
              <div class="text-base font-semibold text-gray-900">{{ getUptime() }}%</div>
              <div class="text-[0.6rem] text-gray-500">Uptime</div>
            </div>
            <div class="text-center">
              <div class="text-base font-semibold text-gray-900">{{ formatDate(screen.last_ping) }}</div>
              <div class="text-[0.6rem] text-gray-500">Last Ping</div>
            </div>
            @if (getStatsGridClass().includes('grid-cols-3')) {
              <div class="text-center">
                <div class="text-base font-semibold text-gray-900">{{ getErrorCount() }}</div>
                <div class="text-[0.6rem] text-gray-500">Errors</div>
              </div>
            }
          </div>

          <!-- Current Playlist Info -->
          <div class="border-t border-gray-200 pt-2">
            <h4 class="text-xs font-medium text-gray-900 mb-1">Current Playlist</h4>
            @if (isScreenOffline()) {
              @if (screen.current_playlist) {
                <div class="flex items-center justify-between bg-white p-1.5 rounded">
                  <span class="text-xs font-medium text-gray-900 truncate">{{ currentPlaylistName }}</span>
                  <span class="text-[0.6rem] text-gray-500">Offline</span>
                </div>
              } @else {
                <p class="text-xs text-gray-500">Screen offline - no playlist</p>
              }
            } @else if (screen.status === 'error') {
              @if (screen.current_playlist) {
                <div class="flex items-center justify-between bg-white p-1.5 rounded">
                  <span class="text-xs font-medium text-gray-900 truncate">{{ currentPlaylistName }}</span>
                  <span class="text-[0.6rem] text-red-500">Error</span>
                </div>
              } @else {
                <p class="text-xs text-gray-500">Screen error - no playlist</p>
              }
            } @else if (screen.status === 'maintenance') {
              @if (screen.current_playlist) {
                <div class="flex items-center justify-between bg-white p-1.5 rounded">
                  <span class="text-xs font-medium text-gray-900 truncate">{{ currentPlaylistName }}</span>
                  <span class="text-[0.6rem] text-yellow-500">Maint</span>
                </div>
              } @else {
                <p class="text-xs text-gray-500">Maintenance - no playlist</p>
              }
            } @else if (screen.current_playlist && playlists[screen.id]) {
              <div class="flex items-center justify-between bg-white p-1.5 rounded">
                <span class="text-xs font-medium text-gray-900 truncate">{{ currentPlaylistName }}</span>
                <span class="text-[0.6rem] text-green-500">Active</span>
              </div>
              <!-- Playlist preview -->
              <div class="mt-1.5 bg-white p-1.5 rounded">
                <div class="flex space-x-1 overflow-x-auto">
                  @for (item of getPlaylistItems() | slice:0:4; track item.id) {
                    <div class="flex-shrink-0 w-8 h-8 rounded border overflow-hidden">
                      @switch (item.type) {
                        @case ('image') {
                          <img [src]="item.content.thumbnail || item.content.url" 
                               [alt]="item.name" 
                               class="w-full h-full object-cover">
                        }
                        @case ('video') {
                          <div class="w-full h-full bg-blue-100 flex items-center justify-center">
                            <span class="material-icons text-blue-500 text-[0.6rem]">play_arrow</span>
                          </div>
                        }
                        @case ('webpage') {
                          <div class="w-full h-full bg-green-100 flex items-center justify-center">
                            <span class="material-icons text-green-500 text-[0.6rem]">language</span>
                          </div>
                        }
                        @case ('ticker') {
                          <div class="w-full h-full bg-purple-100 flex items-center justify-center">
                            <span class="material-icons text-purple-500 text-[0.6rem]">text_scroll</span>
                          </div>
                        }
                      }
                    </div>
                  }
                  @if (getPlaylistItemsCount() > 4) {
                    <div class="flex-shrink-0 w-8 h-8 rounded border bg-gray-100 flex items-center justify-center">
                      <span class="text-[0.6rem] text-gray-500">+{{ getPlaylistItemsCount() - 4 }}</span>
                    </div>
                  }
                </div>
              </div>
            } @else if (screen.current_playlist) {
              <div class="flex items-center justify-between bg-white p-1.5 rounded">
                <span class="text-xs font-medium text-gray-900 truncate">{{ currentPlaylistName }}</span>
                <span class="text-[0.6rem] text-blue-500">Loading...</span>
              </div>
            } @else {
              <p class="text-xs text-gray-500">No playlist assigned</p>
            }
          </div>

          <!-- Tags -->
          @if (screen.tags && screen.tags.length > 0) {
            <div class="flex flex-wrap gap-1">
              @for (tag of screen.tags; track tag) {
                <span class="px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-700 text-[0.6rem] font-medium">
                  {{ tag }}
                </span>
              }
            </div>
          }

          <!-- Action Buttons -->
          <div class="flex justify-end gap-1 pt-2 border-t border-gray-200">
            <button
              (click)="handleEdit($event)"
              class="px-2 py-1 text-gray-600 hover:bg-gray-100 rounded transition-colors text-[0.6rem] font-medium flex items-center gap-1"
            >
              <span class="material-icons text-[0.7rem]">settings</span>
              Settings
            </button>
            <button
              (click)="handleDelete($event)"
              class="px-2 py-1 text-red-600 hover:bg-red-50 rounded transition-colors text-[0.6rem] font-medium flex items-center gap-1"
            >
              <span class="material-icons text-[0.7rem]">delete</span>
              Delete
            </button>
          </div>
        </div>
      }
    </div>
  `,
})
export class ScreenCardComponent implements OnInit, OnChanges {
  @Input({ required: true }) screen!: Screen;
  @Output() edit = new EventEmitter<Screen>();
  @Output() viewDetails = new EventEmitter<Screen>();
  @Output() delete = new EventEmitter<Screen>();

  isExpanded = false;
  currentPlaylistName = '';
  playlists: PlaylistMap = {};

  constructor(private playlistService: PlaylistService) {}

  ngOnInit(): void {
    this.loadPlaylistData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['screen']) {
      // Reset the playlists object when screen changes
      this.playlists = {};
      this.currentPlaylistName = '';
      this.loadPlaylistData();
    }
  }

  loadPlaylistData(): void {
    if (!this.screen?.current_playlist) {
      this.playlists[this.screen.id] = null;
      this.currentPlaylistName = '';
      return;
    }

    // Reset the playlist for this screen while loading
    this.playlists[this.screen.id] = null;
    this.currentPlaylistName = 'Loading...';

    this.playlistService.getPlaylist(this.screen.current_playlist).subscribe({
      next: (playlist: Playlist | undefined) => {
        if (playlist) {
          // Ensure items array exists and is populated
          if (!playlist.items || !Array.isArray(playlist.items)) {
            playlist.items = [];
          }
          this.playlists[this.screen.id] = { ...playlist };
          this.currentPlaylistName = playlist.name || 'Unnamed Playlist';
        } else {
          this.playlists[this.screen.id] = null;
          this.currentPlaylistName = 'Playlist not found';
        }
      },
      error: (err: any) => {
        console.error('Error loading playlist:', err);
        this.playlists[this.screen.id] = null;
        this.currentPlaylistName = 'Error loading playlist';
      }
    });
  }

  getCardContainerClass(): string {
    if (this.getScreenOrientation() === 'portrait') {
      return 'w-full max-w-[200px] mx-auto'; // More compact portrait card
    }
    return 'w-full max-w-[220px] mx-auto'; // More compact landscape card
  }

  getScreenMockupClass(): string {
    if (this.getScreenOrientation() === 'portrait') {
      return 'w-32 h-48'; // Better proportioned portrait screen mockup
    }
    return 'w-48 h-32'; // Better proportioned landscape screen mockup
  }

  getScreenContentClass(): string {
    if (this.getScreenOrientation() === 'portrait') {
      return 'w-full h-full'; // Full size for portrait
    }
    return 'w-full h-full'; // Full size for landscape
  }

  getScreenOrientation(): 'landscape' | 'portrait' {
    if (this.screen.orientation) {
      return this.screen.orientation;
    }
    
    // Fall back to auto-detection from resolution string
    if (this.screen.resolution) {
      const resolutionMatch = this.screen.resolution.match(/(\d+)x(\d+)/);
      if (resolutionMatch) {
        const width = parseInt(resolutionMatch[1], 10);
        const height = parseInt(resolutionMatch[2], 10);
        return width > height ? 'landscape' : 'portrait';
      }
    }
    
    // Default to landscape if we can't determine orientation
    return 'landscape';
  }

  getStatusBadgeClass(): string {
    switch (this.screen.status) {
      case 'online':
        return 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
      case 'offline':
        return 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800';
      case 'maintenance':
        return 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800';
      case 'error':
        return 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800';
      default:
        return 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800';
    }
  }

  getStatsGridClass(): string {
    if (this.getScreenOrientation() === 'portrait') {
      return 'grid grid-cols-2 gap-3'; // Two columns for portrait
    }
    return 'grid grid-cols-3 gap-4'; // Three columns for landscape (wider cards)
  }

  getStatusColor(): string {
    switch (this.screen.status) {
      case 'online':
        return 'bg-green-400';
      case 'offline':
        return 'bg-gray-500';
      case 'maintenance':
        return 'bg-yellow-400';
      case 'error':
        return 'bg-red-400';
      default:
        return 'bg-gray-500';
    }
  }

  getStatusBadgeColor(): string {
    switch (this.screen.status) {
      case 'online':
        return 'bg-green-100 text-green-700';
      case 'offline':
        return 'bg-gray-100 text-gray-600';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-700';
      case 'error':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  }

  // Check if screen is offline based on last ping (more than 5 minutes ago)
  isScreenOffline(): boolean {
    if (!this.screen || !this.screen.last_ping) return true;
    
    // Parse the last_ping date
    const lastPing = new Date(this.screen.last_ping);
    const now = new Date();
    
    // Check if lastPing is a valid date
    if (isNaN(lastPing.getTime())) return true;
    
    // Calculate the difference in minutes
    const diffInMinutes = (now.getTime() - lastPing.getTime()) / (1000 * 60);
    
    // Consider offline if last ping was more than 5 minutes ago
    return diffInMinutes > 5;
  }

  // Add this method to safely check for errors
  hasErrors(): boolean {
    return this.screen.analytics && 
           this.screen.analytics.errors && 
           typeof this.screen.analytics.errors.count === 'number' && 
           this.screen.analytics.errors.count > 0;
  }

  // Helper method to safely get playlist items
  getPlaylistItems(): PlaylistItem[] {
    if (this.playlists[this.screen.id] && this.playlists[this.screen.id]!.items) {
      return this.playlists[this.screen.id]!.items;
    }
    return [];
  }

  // Helper method to safely get playlist items count
  getPlaylistItemsCount(): number {
    if (this.playlists[this.screen.id] && this.playlists[this.screen.id]!.items) {
      return this.playlists[this.screen.id]!.items.length;
    }
    return 0;
  }

  // Add this method to safely get the error count
  getErrorCount(): number {
    if (this.screen.analytics && 
        this.screen.analytics.errors && 
        typeof this.screen.analytics.errors.count === 'number') {
      return this.screen.analytics.errors.count;
    }
    return 0;
  }

  // Add a safe method to get uptime
  getUptime(): number {
    if (this.screen.analytics && 
        typeof this.screen.analytics.uptime === 'number') {
      return this.screen.analytics.uptime;
    }
    return 0;
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'Never';
    
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffSec = Math.floor(diffMs / 1000);
      const diffMin = Math.floor(diffSec / 60);
      const diffHour = Math.floor(diffMin / 60);
      const diffDay = Math.floor(diffHour / 24);
      
      if (diffSec < 60) return `${diffSec}s ago`;
      if (diffMin < 60) return `${diffMin}m ago`;
      if (diffHour < 24) return `${diffHour}h ago`;
      if (diffDay < 7) return `${diffDay}d ago`;
      
      return date.toLocaleDateString();
    } catch (e) {
      return 'Invalid date';
    }
  }

  toggleExpand(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isExpanded = !this.isExpanded;
  }

  handleEdit(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.edit.emit(this.screen);
  }

  handleDelete(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.delete.emit(this.screen);
  }

  handleViewDetails(event: MouseEvent): void {
    // Prevent navigation if clicking on action buttons
    if ((event.target as HTMLElement).closest('button')) {
      return;
    }
    
    event.preventDefault();
    event.stopPropagation();
    this.viewDetails.emit(this.screen);
  }
}