// playlist-details-dialog.component.ts
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Playlist } from '../../../../../models/playlist.model';
import { PlaylistService } from '../../../../playlists/services/playlist.service';

// Add interface for color picker modal
interface ColorPickerModal {
  show: boolean;
  selectedColor: string;
}

@Component({
  selector: 'app-playlist-details-dialog',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="fixed inset-0 bg-gray-100/95 backdrop-blur-sm flex items-center justify-center z-50 overflow-y-auto py-8">
      <!-- Main Paper Card -->
      <div class="bg-white rounded-lg w-full max-w-5xl mx-4 my-auto shadow-[0_2px_15px_-3px_rgba(0,0,0,0.07),0_10px_20px_-2px_rgba(0,0,0,0.04)] transform transition-all duration-300 max-h-[90vh] flex flex-col">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
          @if (loading) {
            <div class="h-6 w-48 bg-gray-100 animate-pulse rounded"></div>
          } @else {
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="h-10 w-10 bg-blue-50 rounded-full flex items-center justify-center">
                  <span class="material-icons text-blue-500">playlist_play</span>
                </div>
                <div class="flex items-center gap-3">
                  <div>
                    <div class="flex items-center gap-2">
                      <h2 class="text-xl text-gray-800">{{ playlist?.name }}</h2>
                      <!-- Color Indicator -->
                      <div class="w-5 h-5 rounded-full border-2 border-white shadow-sm cursor-pointer hover:scale-110 transition-transform"
                        [style.background-color]="playlist?.color || '#3B82F6'"
                        [title]="'Playlist color: ' + (playlist?.color || '#3B82F6')"
                        (click)="openColorPicker()">
                      </div>
                    </div>
                    <p class="text-sm text-gray-500">Modified {{ playlist?.lastModified | date:'medium' }}</p>
                  </div>
                </div>
              </div>
              <button
                (click)="onClose.emit()"
                class="h-8 w-8 rounded-full hover:bg-gray-50 flex items-center justify-center"
              >
                <span class="material-icons text-gray-400">close</span>
              </button>
            </div>
          }
        </div>

        <!-- Content Area -->
        <div class="p-8 overflow-y-auto flex-1">
          @if (loading) {
            <div class="flex justify-center py-12">
              <div class="animate-spin rounded-full h-8 w-8 border-2 border-blue-400 border-t-transparent"></div>
            </div>
          } @else if (error) {
            <div class="bg-red-50 rounded-lg p-4 text-red-600 flex items-center gap-3">
              <span class="material-icons">error_outline</span>
              <p>{{ error }}</p>
              <button
                (click)="loadPlaylist()"
                class="ml-auto text-sm text-red-600 hover:text-red-700"
              >
                Retry
              </button>
            </div>
          } @else if (playlist) {
            <div class="space-y-8">
              <!-- Stats Cards -->
              <div class="grid grid-cols-3 gap-6">
                <!-- Duration Card -->
                <div class="bg-white rounded-lg p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="h-8 w-8 bg-blue-50 rounded-full flex items-center justify-center">
                      <span class="material-icons text-blue-500">timer</span>
                    </div>
                    <p class="text-gray-600">Duration</p>
                  </div>
                  <p class="text-2xl font-light text-gray-800">{{ formatDuration(playlist.duration) }}</p>
                </div>

                <!-- Items Count Card -->
                <div class="bg-white rounded-lg p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="h-8 w-8 bg-emerald-50 rounded-full flex items-center justify-center">
                      <span class="material-icons text-emerald-500">view_list</span>
                    </div>
                    <p class="text-gray-600">Items</p>
                  </div>
                  <p class="text-2xl font-light text-gray-800">{{ playlist.items.length }}</p>
                </div>

                <!-- Status Card -->
                <div class="bg-white rounded-lg p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="h-8 w-8 bg-purple-50 rounded-full flex items-center justify-center">
                      <span class="material-icons text-purple-500">radio_button_checked</span>
                    </div>
                    <p class="text-gray-600">Status</p>
                  </div>
                  <p class="text-2xl font-light capitalize" 
                     [class]="getStatusColor(playlist.status)">
                    {{ playlist.status }}
                  </p>
                </div>
              </div>

              <!-- Description Section -->
              @if (playlist.description) {
                <div class="bg-gray-50 rounded-lg p-6">
                  <h3 class="text-gray-700 font-medium mb-3 flex items-center gap-2">
                    <span class="material-icons text-gray-400">description</span>
                    Description
                  </h3>
                  <p class="text-gray-600 leading-relaxed">{{ playlist.description }}</p>
                </div>
              }

              <!-- Playlist Items -->
              <div class="bg-white rounded-lg border border-gray-100 shadow-sm">
                <div class="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                  <h3 class="text-gray-700 font-medium flex items-center gap-2">
                    <span class="material-icons text-gray-400">format_list_bulleted</span>
                    Playlist Items
                  </h3>
                  <span class="text-sm text-gray-500">
                    {{ playlist.items.length }} items • {{ formatDuration(playlist.duration) }}
                  </span>
                </div>

                <div class="divide-y divide-gray-100 max-h-[400px] overflow-y-auto">
                  @for (item of playlist.items; track item.id) {
                    <div class="p-4 hover:bg-gray-50 transition-colors">
                      <div class="flex items-center gap-4">
                        <!-- Number -->
                        <div class="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
                          {{ playlist.items.indexOf(item) + 1 }}
                        </div>

                        <!-- Thumbnail -->
                        <div class="h-16 w-16 rounded overflow-hidden bg-gray-100 flex items-center justify-center">
                          @if (item.type === 'image') {
                            <img [src]="item.content.url" [alt]="item.name" 
                                 class="h-full w-full object-cover" />
                          } @else if (item.type === 'video' && item.content.thumbnail) {
                            <div class="relative h-full w-full">
                              <img [src]="item.content.thumbnail" [alt]="item.name" 
                                   class="h-full w-full object-cover" />
                              <div class="absolute inset-0 flex items-center justify-center bg-black/20">
                                <span class="material-icons text-white">play_circle</span>
                              </div>
                            </div>
                          } @else {
                            <span class="material-icons text-gray-400">{{ getItemIcon(item.type) }}</span>
                          }
                        </div>

                        <!-- Info -->
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-800">{{ item.name }}</h4>
                          <div class="flex items-center gap-4 mt-1">
                            <span class="text-sm text-gray-500 capitalize">{{ item.type }}</span>
                            <span class="text-sm text-gray-500 flex items-center gap-1">
                              <span class="material-icons text-gray-400 text-sm">schedule</span>
                              {{ formatDuration(item.duration) }}
                            </span>
                          </div>

                          <!-- Settings Tags -->
                          <div class="flex gap-2 mt-2">
                            @if (item.settings.transition !== 'none') {
                              <span class="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                                {{ item.settings.transition }}
                              </span>
                            }
                            @if (item.settings.loop) {
                              <span class="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                                Loop
                              </span>
                            }
                            @if (item.settings.muted) {
                              <span class="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                                Muted
                              </span>
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  }
                </div>
              </div>
            </div>
          }
        </div>

        <!-- Footer -->
        @if (playlist) {
          <div class="p-6 border-t border-gray-100 flex justify-end gap-3">
            <button
              (click)="onClose.emit()"
              class="px-6 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
            >
              Close
            </button>
            <button
              (click)="onAssign.emit(playlist)"
              class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors shadow-sm"
            >
              Assign Playlist
            </button>
          </div>
        }
      </div>

      <!-- Color Picker Modal -->
      @if (colorPickerModal.show) {
        <div class="fixed inset-0 z-60 flex items-center justify-center bg-black bg-opacity-50">
          <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Choose Playlist Color</h3>
              <button (click)="closeColorPicker()" class="text-gray-400 hover:text-gray-600">
                <span class="material-icons">close</span>
              </button>
            </div>
            
            <div class="mb-4">
              <p class="text-sm text-gray-600 mb-3">{{ playlist?.name }}</p>
              
              <!-- Predefined Colors -->
              <div class="grid grid-cols-8 gap-2 mb-4">
                @for (color of predefinedColors; track color) {
                  <div class="w-8 h-8 rounded-full cursor-pointer border-2 border-gray-200 hover:border-gray-400 transition-colors"
                    [style.background-color]="color"
                    [class.ring-2]="colorPickerModal.selectedColor === color"
                    [class.ring-blue-500]="colorPickerModal.selectedColor === color"
                    (click)="selectColor(color)">
                  </div>
                }
              </div>
              
              <!-- Custom Color Input -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Custom Color</label>
                <div class="flex items-center space-x-2">
                  <input type="color" 
                    [(ngModel)]="colorPickerModal.selectedColor"
                    class="w-12 h-8 rounded border border-gray-300 cursor-pointer">
                  <input type="text" 
                    [(ngModel)]="colorPickerModal.selectedColor"
                    placeholder="#3B82F6"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
              </div>
              
              <!-- Preview -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
                <div class="flex items-center p-3 bg-gray-50 rounded-md">
                  <div class="w-4 h-4 rounded-full mr-3 border-2 border-white shadow-sm"
                    [style.background-color]="colorPickerModal.selectedColor">
                  </div>
                  <span class="text-sm text-gray-700">{{ playlist?.name }}</span>
                </div>
              </div>
            </div>
            
            <div class="flex justify-end space-x-3">
              <button (click)="closeColorPicker()" 
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                Cancel
              </button>
              <button (click)="savePlaylistColor()" 
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                Save Color
              </button>
            </div>
          </div>
        </div>
      }
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }

    /* Custom scrollbar */
    .overflow-y-auto {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    }

    .overflow-y-auto::-webkit-scrollbar {
      width: 6px;
    }

    .overflow-y-auto::-webkit-scrollbar-track {
      background: transparent;
    }

    .overflow-y-auto::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.5);
      border-radius: 3px;
    }

    /* Color picker styles */
    .color-option {
      transition: all 0.2s ease;
      position: relative;
    }
    
    .color-option:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .color-option.selected {
      transform: scale(1.1);
    }
    
    .color-option.selected::after {
      content: '✓';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-weight: bold;
      font-size: 0.75rem;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    /* Color indicator hover effect */
    .playlist-color-indicator {
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .playlist-color-indicator:hover {
      transform: scale(1.2);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .playlist-color-indicator:active {
      transform: scale(1.1);
    }

    /* Custom color input styling */
    input[type="color"] {
      -webkit-appearance: none;
      border: none;
      cursor: pointer;
    }
    
    input[type="color"]::-webkit-color-swatch-wrapper {
      padding: 0;
    }
    
    input[type="color"]::-webkit-color-swatch {
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
    }
  `]
})
export class PlaylistDetailsDialogComponent implements OnInit {
  @Input({ required: true }) playlistId!: string;
  @Output() onAssign = new EventEmitter<Playlist>();
  @Output() onClose = new EventEmitter<void>();

  playlist: Playlist | null = null;
  loading = true;
  error: string | null = null;

  // Color picker properties
  colorPickerModal: ColorPickerModal = {
    show: false,
    selectedColor: '#3B82F6'
  };
  
  // Predefined colors for playlists
  predefinedColors = [
    '#3B82F6', // Blue
    '#10B981', // Emerald
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#8B5CF6', // Violet
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange
    '#EC4899', // Pink
    '#6B7280', // Gray
    '#14B8A6', // Teal
    '#A855F7', // Purple
    '#22C55E', // Green
    '#F43F5E', // Rose
    '#0EA5E9', // Sky
    '#D946EF'  // Fuchsia
  ];

  constructor(private playlistService: PlaylistService) {}

  ngOnInit() {
    this.loadPlaylist();
  }

  loadPlaylist() {
    this.loading = true;
    this.error = null;

    this.playlistService.getPlaylist(this.playlistId).subscribe({
      next: (playlist) => {
        this.playlist = playlist;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading playlist:', error);
        this.error = 'Failed to load playlist details. Please try again.';
        this.loading = false;
      }
    });
  }

  getItemIcon(type: string): string {
    switch (type) {
      case 'video':
        return 'movie';
      case 'image':
        return 'image';
      case 'webpage':
        return 'web';
      default:
        return 'insert_drive_file';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'text-emerald-500';
      case 'draft':
        return 'text-gray-500';
      case 'archived':
        return 'text-amber-500';
      default:
        return 'text-gray-500';
    }
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}s`;
  }

  // Color picker methods
  openColorPicker(): void {
    if (this.playlist) {
      this.colorPickerModal = {
        show: true,
        selectedColor: this.playlist.color || '#3B82F6'
      };
    }
  }
  
  closeColorPicker(): void {
    this.colorPickerModal = {
      show: false,
      selectedColor: '#3B82F6'
    };
  }
  
  selectColor(color: string): void {
    this.colorPickerModal.selectedColor = color;
  }
  
  savePlaylistColor(): void {
    if (this.playlist) {
      const newColor = this.colorPickerModal.selectedColor;
      
      // Update the playlist with the new color
      this.playlistService.updatePlaylist(this.playlist.id, { color: newColor }).subscribe({
        next: (updatedPlaylist) => {
          console.log('Playlist color updated successfully:', updatedPlaylist);
          
          // Update the local playlist object
          this.playlist = updatedPlaylist;
          
          // Close the color picker
          this.closeColorPicker();
        },
        error: (error) => {
          console.error('Error updating playlist color:', error);
          // In a real app, you might want to show an error message to the user
        }
      });
    }
  }
}