// src/app/features/preview/public-playlist-preview.component.ts
import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { PlaylistItem } from '../../models/playlist.model';
import { supabase } from '../../core/services/supabase.config';
import { ConnectivityService } from '../../core/services/connectivity.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-public-playlist-preview',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="fixed inset-0 bg-black flex items-center justify-center">
      <!-- Loading -->
      <div *ngIf="loading" class="text-white text-xl">Loading playlist...</div>
      
      <!-- Error -->
      <div *ngIf="error && !isOffline" class="text-white text-xl">{{ error }}</div>
      
      <!-- Offline Display -->
      <div *ngIf="isOffline" class="text-white text-xl text-center p-8">
        <div class="mb-4">
          <div class="inline-block animate-pulse">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
            </svg>
          </div>
        </div>
        <h2 class="text-2xl font-bold mb-2">OFFLINE MODE</h2>
        <p class="mb-4">Unable to connect to server. Displaying cached content.</p>
        <div class="flex items-center justify-center space-x-2">
          <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          <span>Heartbeat Lost</span>
        </div>
      </div>
      
      <!-- Content -->
      <div *ngIf="!loading && !error && !isOffline" class="w-full h-full relative" [class.tv-display-content]="true" [class.portrait-mode]="isPortraitMode">
        <!-- Display the current image/content -->
        <div *ngFor="let item of items; let i = index" class="absolute inset-0" [class.portrait-media]="isPortraitMode">
          <!-- Image -->
          <img 
            *ngIf="item.type === 'image'" 
            [src]="item.content.url"
            [style.display]="currentIndex === i ? 'block' : 'none'"
            [class]="isPortraitMode ? 'portrait-media-item' : 'w-full h-full object-contain'"
          />
          
          <!-- Video -->
          <video 
            #videoPlayer
            *ngIf="item.type === 'video'" 
            [src]="item.content.url"
            [style.display]="currentIndex === i ? 'block' : 'none'"
            [class]="isPortraitMode ? 'portrait-media-item' : 'w-full h-full object-contain'"
            [muted]="item.settings.muted ?? true"
            [loop]="false"
            (ended)="handleVideoEnded()"
          ></video>
        </div>
        
        <!-- Heartbeat indicator -->
        <div class="absolute top-4 right-4 z-10">
          <div class="flex items-center bg-black bg-opacity-50 rounded-full px-3 py-1">
            <div class="w-3 h-3 rounded-full mr-2" 
                 [ngClass]="isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'"></div>
            <span class="text-white text-sm">{{ isConnected ? 'ONLINE' : 'OFFLINE' }}</span>
          </div>
        </div>
        
        <!-- Progress bar at bottom
        <div class="absolute bottom-0 left-0 right-0 h-1 bg-gray-800">
          <div 
            class="h-full bg-blue-500 transition-all"
            [style.width.%]="progressPercentage"
          ></div>
        </div> -->
      </div>
    </div>
  `
})
export class PublicPlaylistPreviewComponent implements OnInit, OnDestroy {
  @ViewChild('videoPlayer') videoPlayer?: ElementRef<HTMLVideoElement>;
  
  loading = true;
  error: string | null = null;
  items: PlaylistItem[] = [];
  currentIndex = 0;
  isPortraitMode = false;
  isOffline = false;
  isConnected = true;
  
  // Timer variables
  private slideTimer: any = null;
  private startTime: number = 0;
  private currentDuration: number = 0;
  progressPercentage: number = 0;
  private animationFrameId: number | null = null;
  
  // Subscriptions
  private connectivitySubscription: Subscription | null = null;
  
  constructor(
    private route: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private connectivityService: ConnectivityService
  ) {}
  
  ngOnInit() {
    this.loadPlaylist();
    this.checkScreenOrientation();
    this.setupConnectivityListener();
  }

  private setupConnectivityListener() {
    this.connectivitySubscription = this.connectivityService.isOnline$.subscribe(isOnline => {
      this.isConnected = isOnline;
      this.isOffline = !isOnline && !this.loading;
      
      // If we were offline and now we're back online, reload the playlist
      if (!this.isOffline && isOnline && this.items.length === 0) {
        this.loadPlaylist();
      }
    });
  }

  private checkScreenOrientation() {
    // Check URL params for screen orientation data
    const screenId = this.route.snapshot.queryParamMap.get('screenId');
    if (screenId) {
      this.loadScreenOrientation(screenId);
    }
  }

  private async loadScreenOrientation(screenId: string) {
    try {
      const { data: screen, error } = await supabase
        .from('screens')
        .select('settings')
        .eq('id', screenId)
        .single();
      
      if (!error && screen?.settings?.screen_rotation) {
        this.isPortraitMode = screen.settings.screen_rotation === 90 || screen.settings.screen_rotation === 270;
        console.log('Screen orientation loaded:', this.isPortraitMode ? 'portrait' : 'landscape');
      }
    } catch (error) {
      console.error('Error loading screen orientation:', error);
    }
  }
  
  ngOnDestroy() {
    this.clearTimers();
    if (this.connectivitySubscription) {
      this.connectivitySubscription.unsubscribe();
    }
  }
  
  private clearTimers() {
    if (this.slideTimer) {
      clearTimeout(this.slideTimer);
      this.slideTimer = null;
    }
    
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }
  
  async loadPlaylist() {
    try {
      const playlistId = this.route.snapshot.paramMap.get('id');
      console.log('Loading playlist:', playlistId);
      
      if (!playlistId) {
        throw new Error('No playlist ID provided');
      }
      
      const { data, error } = await supabase
        .from('playlists')
        .select(`
          *,
          items:playlist_items(*)
        `)
        .eq('id', playlistId)
        .single();
        
      if (error) throw error;
      
      console.log('Playlist data loaded:', data);
      
      // Map the items
      this.items = (data.items || []).map((item: any) => ({
        id: item.id,
        type: item.type || 'image',
        name: item.name || '',
        duration: item.duration || 10,
        content: {
          url: item.content_url || '',
          thumbnail: item.thumbnail_url || ''
        },
        settings: {
          transition: item.transition || 'fade',
          transitionDuration: item.transition_duration || 0.5,
          scaling: item.scaling || 'fit',
          muted: item.muted || true,
          loop: item.loop || false
        },
        schedule: null
      }));
      
      console.log('Mapped items:', this.items);
      
      if (this.items.length === 0) {
        throw new Error('Playlist has no items');
      }
      
      this.loading = false;
      this.showCurrentItem();
      
    } catch (err: any) {
      console.error('Error loading playlist:', err);
      this.error = err.message || 'Failed to load playlist';
      this.loading = false;
      
      // If we're offline, show offline mode instead of error
      if (!this.connectivityService.isOnline$.value) {
        this.isOffline = true;
      }
    }
  }
  
  showCurrentItem() {
    // Clear any existing timers
    this.clearTimers();
    
    const currentItem = this.items[this.currentIndex];
    console.log(`Showing item ${this.currentIndex + 1}/${this.items.length}: ${currentItem.name} (${currentItem.duration}s)`);
    
    if (currentItem.type === 'video') {
      // For videos, wait for the video to end (handling through the ended event)
      setTimeout(() => {
        if (this.videoPlayer?.nativeElement) {
          this.videoPlayer.nativeElement.currentTime = 0;
          this.videoPlayer.nativeElement.play().catch(err => {
            console.error('Error playing video:', err);
            // If video fails to play, move to next slide after the defined duration
            this.startTimerForCurrentItem();
          });
        } else {
          // If video player is not available for some reason, fall back to timer
          this.startTimerForCurrentItem();
        }
      }, 0);
    } else {
      // For images and other content, use the defined duration
      this.startTimerForCurrentItem();
    }
  }
  
  startTimerForCurrentItem() {
    const currentItem = this.items[this.currentIndex];
    this.currentDuration = currentItem.duration * 1000; // Convert to milliseconds
    this.startTime = Date.now();
    
    // Set up the timer to move to the next item
    this.slideTimer = setTimeout(() => {
      this.nextSlide();
    }, this.currentDuration);
    
    // Start progress bar animation
    this.updateProgressBar();
  }
  
  updateProgressBar() {
    const elapsed = Date.now() - this.startTime;
    this.progressPercentage = Math.min((elapsed / this.currentDuration) * 100, 100);
    
    if (this.progressPercentage < 100) {
      this.animationFrameId = requestAnimationFrame(() => this.updateProgressBar());
    }
  }
  
  handleVideoEnded() {
    console.log('Video ended naturally');
    this.nextSlide();
  }
  
  nextSlide() {
    this.clearTimers();
    this.currentIndex = (this.currentIndex + 1) % this.items.length;
    this.progressPercentage = 0;
    this.showCurrentItem();
  }
}