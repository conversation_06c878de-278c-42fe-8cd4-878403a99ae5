<div
  class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
>
  <div class="w-full max-w-md p-6 bg-white rounded shadow-xl">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-medium text-gray-900">New Playlist</h2>
      <button
        (click)="onCancel.emit()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <span class="material-icons">close</span>
      </button>
    </div>

    <form
      [formGroup]="playlistForm"
      (ngSubmit)="handleSubmit()"
      class="space-y-5"
    >
      <div>
        <input
          type="text"
          formControlName="name"
          placeholder="Playlist name"
          class="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg focus:ring-2 focus:ring-blue-500 text-lg font-medium placeholder:text-gray-400 shadow-md"
          required
        />
      </div>

      <textarea
        formControlName="description"
        placeholder="Add description (optional)"
        rows="2"
        class="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none text-gray-600 shadow-md"
      ></textarea>

      <!-- Color Picker -->
      <div class="space-y-3">
        <label class="block text-sm font-medium text-gray-700">
          Playlist Color
        </label>
        <div class="flex flex-wrap gap-2">
          @for (color of colorOptions; track color) {
            <button
              type="button"
              (click)="selectColor(color)"
              class="w-8 h-8 rounded-full border-2 transition-all duration-200"
              [class.border-gray-300]="selectedColor !== color"
              [class.border-gray-800]="selectedColor === color"
              [style.background-color]="color"
              [attr.aria-label]="'Select color ' + color"
            ></button>
          }
        </div>
        <div class="flex items-center gap-2 pt-1">
          <span class="text-sm text-gray-500">Selected:</span>
          <div 
            class="w-6 h-6 rounded-full border border-gray-300"
            [style.background-color]="selectedColor"
          ></div>
          <span class="text-sm font-mono text-gray-600">{{ selectedColor }}</span>
        </div>
      </div>

      <div class="pt-4 flex justify-end gap-3">
        <button
          type="button"
          (click)="onCancel.emit()"
          class="px-6 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          [disabled]="!playlistForm.valid || isSubmitting"
          class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
        >
          @if (isSubmitting) {
          <span class="material-icons animate-spin text-sm">refresh</span>
          } @else { Create }
        </button>
      </div>
    </form>
  </div>
</div>
