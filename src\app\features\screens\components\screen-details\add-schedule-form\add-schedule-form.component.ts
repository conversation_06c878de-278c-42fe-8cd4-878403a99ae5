import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { PlaylistSchedule, PlaylistScheduleBase } from '../../../../../models/screen.model';
import { Playlist } from '../../../../../models/playlist.model';
import { PlaylistService } from '../../../../playlists/services/playlist.service';

interface PlaylistScheduleExtended extends PlaylistScheduleBase {
  schedule_type?: 'weekly' | 'specific_date';
  specific_date?: string;
}

@Component({
  selector: 'app-add-schedule-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './add-schedule-form.component.html',
})
export class AddScheduleFormComponent implements OnInit {
  @Input() initialSchedule: PlaylistScheduleExtended | null = null;
  @Input() availablePlaylists: Playlist[] = []; // Accept playlists as input
  @Output() onSubmit = new EventEmitter<PlaylistSchedule>();
  @Output() onCancel = new EventEmitter<void>();

  scheduleForm: FormGroup;
  loading = false;
  selectedDays: string[] = [];
  today = new Date().toISOString().split('T')[0];
  daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  constructor(
    private fb: FormBuilder,
    private playlistService: PlaylistService
  ) {
    this.scheduleForm = this.fb.group({
      schedule_type: ['weekly'],
      playlist_id: ['', [Validators.required, this.validatePlaylistSelection.bind(this)]],
      start_time: ['', Validators.required],
      end_time: ['', Validators.required],
      specific_date: [this.today],
      priority: [2],
      days_of_week: [[], this.validateDaysOfWeek]
    });

    // Update validation when schedule type changes
    this.scheduleForm.get('schedule_type')?.valueChanges.subscribe(type => {
      const specificDateControl = this.scheduleForm.get('specific_date');
      const daysOfWeekControl = this.scheduleForm.get('days_of_week');
      
      if (type === 'specific_date') {
        specificDateControl?.setValidators(Validators.required);
        daysOfWeekControl?.clearValidators();
      } else {
        specificDateControl?.clearValidators();
        daysOfWeekControl?.setValidators(this.validateDaysOfWeek);
      }
      
      specificDateControl?.updateValueAndValidity();
      daysOfWeekControl?.updateValueAndValidity();
    });
  }

  ngOnInit() {
    // Only load playlists if they weren't provided as input
    if (this.availablePlaylists.length === 0) {
      this.loadPlaylists();
    }
  }

  loadPlaylists() {
    this.loading = true;
    // Check if we need to filter by area
    // We might need to get the area ID from the screen context
    this.playlistService.getPlaylists().subscribe({
      next: (playlists) => {
        this.availablePlaylists = playlists || [];
        console.log('Loaded playlists:', this.availablePlaylists);
        console.log('Playlist IDs:', this.availablePlaylists.map(p => p.id));
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading playlists:', error);
        this.availablePlaylists = [];
        this.loading = false;
      }
    });
  }

  isDaySelected(day: string): boolean {
    return this.selectedDays.includes(day);
  }

  onDayChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    const day = checkbox.value;
    
    if (checkbox.checked) {
      if (!this.selectedDays.includes(day)) {
        this.selectedDays.push(day);
      }
    } else {
      this.selectedDays = this.selectedDays.filter(d => d !== day);
    }
    
    // Update the form control with the new selectedDays array
    this.scheduleForm.get('days_of_week')?.setValue(this.selectedDays);
    console.log('Updated days:', this.selectedDays);
  }

  // Custom validator for days of week
  validateDaysOfWeek(control: any) {
    const value = control.value;
    if (!value || value.length === 0) {
      return { required: true };
    }
    return null;
  }

  // Custom validator for playlist selection
  validatePlaylistSelection(control: any) {
    const value = control.value;
    if (!value) {
      return { required: true };
    }
    return null;
  }

  handleSubmit() {
    if (this.scheduleForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.scheduleForm.controls).forEach(key => {
        this.scheduleForm.get(key)?.markAsTouched();
      });
      return;
    }

    const formValues = this.scheduleForm.value;
    
    // Ensure playlists are loaded before proceeding
    if (this.availablePlaylists.length === 0) {
      console.warn('No playlists available, attempting to reload...');
      this.loadPlaylists();
      // We might need to delay the submission to allow playlists to load
      setTimeout(() => {
        this.processScheduleSubmission(formValues);
      }, 1000);
      return;
    }
    
    this.processScheduleSubmission(formValues);
  }

  private processScheduleSubmission(formValues: any) {
    console.log('Processing schedule submission with form values:', formValues);
    console.log('Available playlists:', this.availablePlaylists);
    
    // Ensure we have playlists loaded
    if (this.availablePlaylists.length === 0) {
      console.error('No playlists available for selection');
      alert('No playlists available. Please try again.');
      return;
    }
    
    const selectedPlaylist = this.availablePlaylists.find(p => p.id === formValues.playlist_id);
    
    if (!selectedPlaylist) {
      console.error('Selected playlist not found. Available playlists:', this.availablePlaylists);
      console.error('Selected playlist ID:', formValues.playlist_id);
      
      // Log each playlist's ID for debugging
      this.availablePlaylists.forEach((playlist, index) => {
        console.log(`Playlist ${index}: ID=${playlist.id}, Name=${playlist.name}`);
        console.log(`ID comparison: ${playlist.id} === ${formValues.playlist_id} = ${playlist.id === formValues.playlist_id}`);
      });
      
      // Show error to user
      alert('Selected playlist not found. Please try again.');
      return;
    }

    // Make sure we're using the selectedDays array for days_of_week
    // Also ensure we have at least one day selected
    if (!this.selectedDays || this.selectedDays.length === 0) {
      console.error('No days selected for schedule');
      alert('Please select at least one day for the schedule.');
      return;
    }

    const schedule: PlaylistSchedule = {
      playlist_id: formValues.playlist_id,
      playlist_name: selectedPlaylist.name,
      start_time: formValues.start_time,
      end_time: formValues.end_time,
      days_of_week: [...this.selectedDays],  // Use a copy of the array we've been tracking
      priority: formValues.priority
    };

    console.log('Submitting schedule:', schedule);
    this.onSubmit.emit(schedule);
  }
}