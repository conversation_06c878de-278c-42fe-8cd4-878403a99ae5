// area-card.component.ts
import { Component, HostListener, Input, Output, EventEmitter, OnDestroy, OnInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { Area } from '../../../../models/area.model';
import { AreaFacade } from '../../../../core/state/area-state/area.facade';
import { Screen } from '../../../../models/screen.model';
import { TvDisplayComponent } from '../../../../shared/components/tv-display/tv-display.component';
import { PlaylistService } from '../../../playlists/services/playlist.service';
import { Playlist } from '../../../../models/playlist.model';
import { ScreenService } from '../../../screens/services/screen.service';

@Component({
  selector: 'app-area-card',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, TvDisplayComponent],
  templateUrl: './area-card.component.html'
})
export class AreaCardComponent implements OnInit, OnDestroy {
  @Input({ required: true }) area!: Area;
  @Output() edit = new EventEmitter<Area>();
  @Output() delete = new EventEmitter<string>();
  @Output() moveArea = new EventEmitter<{areaId: string, newLocation: string}>;
  @Output() screenClick = new EventEmitter<Screen>(); // Add this line
  @Output() screenDropped = new EventEmitter<{screenId: string, targetAreaId: string}>();
  
  // Store playlists for screens
  playlists: { [key: string]: Playlist | null } = {};
  
  showMenu = false;
  showScreens = true; // Always show screens by default
  showMoveDropdown = false;
  newLocation = '';
  isDragOver = false; // For drag and drop visual feedback
  
  // Hover modal state
  hoveredScreenId: string | null = null;
  hoverTimeout: any = null;
  mouseX = 0;
  mouseY = 0;
  
  // Predefined locations for the dropdown
  locations = [
    'Main Building',
    'North Wing',
    'South Wing',
    'East Wing',
    'West Wing',
    'Conference Center',
    'Lobby',
    'Cafeteria',
    'Reception',
    'Other'
  ];
  
  private destroy$ = new Subject<void>();

  constructor(
    private areaFacade: AreaFacade, 
    private el: ElementRef,
    private playlistService: PlaylistService,
    private cdr: ChangeDetectorRef,
    private screenService: ScreenService
  ) {}

  ngOnInit(): void {
    // Subscribe to any area updates
    this.areaFacade.areas$
      .pipe(takeUntil(this.destroy$))
      .subscribe(areas => {
        const updatedArea = areas.find(a => a.id === this.area.id);
        if (updatedArea) {
          this.area = updatedArea;
          // Load playlists for screens when area updates
          this.loadPlaylistsForScreens();
        }
      });
      
    // Load playlists for screens initially
    this.loadPlaylistsForScreens();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
  }

  // Load playlists for all screens in this area
  loadPlaylistsForScreens(): void {
    if (this.area.screens) {
      this.area.screens.forEach(screen => {
        if (screen.current_playlist && !this.playlists[screen.id]) {
          this.playlistService.getPlaylist(screen.current_playlist).subscribe({
            next: (playlist: Playlist | null) => {
              this.playlists[screen.id] = playlist || null;
              // Force change detection
              this.cdr.markForCheck();
            },
            error: (error: any) => {
              console.error('Error loading playlist for screen:', screen.id, error);
              this.playlists[screen.id] = null;
              // Force change detection
              this.cdr.markForCheck();
            }
          });
        }
      });
    }
  }

  toggleMenu(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showMenu = !this.showMenu;
    this.showMoveDropdown = false;
  }

  toggleScreens(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showScreens = !this.showScreens;
  }

  toggleMoveDropdown(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showMoveDropdown = !this.showMoveDropdown;
    this.newLocation = this.area.location; // Pre-fill with current location
  }

  onEdit(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showMenu = false;
    this.edit.emit(this.area); // Emit the area object
  }

  onToggleStatus(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showMenu = false;
    
    this.areaFacade.toggleAreaStatus(this.area.id);
  }

  onDelete(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showMenu = false;
    
    if (confirm('Are you sure you want to delete this area?')) {
      this.delete.emit(this.area.id); // Emit the area ID
    }
  }

  onMoveArea(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showMenu = false;
    this.toggleMoveDropdown(event);
  }

  confirmMove(): void {
    if (this.newLocation && this.newLocation !== this.area.location) {
      this.moveArea.emit({areaId: this.area.id, newLocation: this.newLocation});
    }
    this.showMoveDropdown = false;
  }

  cancelMove(): void {
    this.showMoveDropdown = false;
    this.newLocation = this.area.location;
  }

  // Click outside handler for menu
  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event): void {
    if (this.showMenu && !this.el.nativeElement.contains(event.target)) {
      this.showMenu = false;
    }
    if (this.showMoveDropdown && !this.el.nativeElement.contains(event.target)) {
      this.showMoveDropdown = false;
      this.newLocation = this.area.location;
    }
  }

  // Drag and drop handlers
  onDragStart(event: DragEvent, screen: Screen): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', screen.id);
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    // Check if we're actually leaving the area card
    if (event.relatedTarget && !this.el.nativeElement.contains(event.relatedTarget)) {
      this.isDragOver = false;
    }
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
    
    if (event.dataTransfer) {
      const screenId = event.dataTransfer.getData('text/plain');
      if (screenId) {
        this.screenDropped.emit({screenId, targetAreaId: this.area.id});
      }
    }
  }

  // Check if screen is offline based on last ping (more than 2 minutes ago)
  isScreenOffline(screen: Screen): boolean {
    if (!screen || !screen.last_ping) return true;
    
    // Parse the last_ping date
    const lastPing = new Date(screen.last_ping);
    const now = new Date();
    
    // Check if lastPing is a valid date
    if (isNaN(lastPing.getTime())) return true;
    
    // Calculate the difference in minutes
    const diffInMinutes = (now.getTime() - lastPing.getTime()) / (1000 * 60);
    
    // Consider offline if last ping was more than 2 minutes ago
    return diffInMinutes > 2;
  }

  // Get screen status class
  getScreenStatusClass(screen: Screen): string {
    // If screen is determined to be offline based on last_ping, mark as offline
    if (this.isScreenOffline(screen)) {
      return 'bg-gray-300';
    }
    
    // Otherwise use the status field
    switch (screen.status) {
      case 'online': return 'bg-green-500';
      case 'offline': return 'bg-gray-300';
      case 'maintenance': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-300';
    }
  }

  // Get screen status text
  getScreenStatusText(screen: Screen): string {
    // If screen is determined to be offline based on last_ping, show as offline
    if (this.isScreenOffline(screen)) {
      return 'Offline (No heartbeat)';
    }
    
    switch (screen.status) {
      case 'online': return 'Online';
      case 'offline': return 'Offline';
      case 'maintenance': return 'Maintenance';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  }

  // Format time for display
  formatTime(timeString: string | null | undefined): string {
    if (!timeString) return 'N/A';
    
    try {
      // If it's a full datetime string
      if (timeString.includes('T')) {
        const date = new Date(timeString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      }
      
      // If it's just a time string (HH:MM:SS)
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        const hours = timeParts[0];
        const minutes = timeParts[1];
        return `${hours}:${minutes}`;
      }
      
      return timeString;
    } catch (error) {
      return 'Invalid time';
    }
  }

  // Get screen orientation
  getScreenOrientation(screen: Screen): 'portrait' | 'landscape' {
    return screen.orientation === 'portrait' ? 'portrait' : 'landscape';
  }
  
  // Get total duration of playlist
  getPlaylistDuration(playlist: Playlist | null): number {
    if (!playlist || !playlist.items) return 0;
    
    return playlist.items.reduce((total, item) => {
      return total + (item.duration || 5); // Default to 5 seconds if duration not set
    }, 0);
  }
  
  // Handle screen hover for modal
  onScreenHover(screenId: string, event: MouseEvent): void {
    this.mouseX = event.clientX;
    this.mouseY = event.clientY;
    
    // Clear any existing timeout
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
    
    // Set timeout to show modal after delay
    this.hoverTimeout = setTimeout(() => {
      this.hoveredScreenId = screenId;
    }, 300); // 300ms delay before showing modal
  }
  
  onScreenLeave(): void {
    // Clear timeout if mouse leaves before modal appears
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }
    
    // Immediately hide modal when leaving the playlist area
    this.hoveredScreenId = null;
  }
  
  // Handle screen click for navigation
  onScreenClick(screen: Screen): void {
    this.screenClick.emit(screen);
  }
  
  // Track mouse movement for positioning
  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    this.mouseX = event.clientX;
    this.mouseY = event.clientY;
  }
  
  // Get modal position with boundary checking
  getModalLeft(): number {
    // Modal width is max 500px (max-w-lg) + some padding
    const modalWidth = 500;
    const windowWidth = window.innerWidth;
    
    // Position to the right of mouse, but ensure it stays within viewport
    let left = this.mouseX + 20;
    if (left + modalWidth > windowWidth) {
      // If it would go off the right edge, position to the left of mouse
      left = this.mouseX - modalWidth - 20;
    }
    
    // Ensure it doesn't go off the left edge either
    return Math.max(10, left);
  }
  
  getModalTop(): number {
    // Modal height is approximately 300px + padding
    const modalHeight = 300;
    const windowHeight = window.innerHeight;
    
    // Position above mouse, but ensure it stays within viewport
    let top = this.mouseY - modalHeight - 20;
    if (top < 10) {
      // If it would go off the top edge, position below mouse
      top = this.mouseY + 20;
    }
    
    // Ensure it doesn't go off the bottom edge either
    return Math.min(windowHeight - modalHeight - 10, top);
  }
  
  // Get playlist for hovered screen
  getHoveredPlaylist(): Playlist | null {
    if (this.hoveredScreenId && this.playlists[this.hoveredScreenId]) {
      return this.playlists[this.hoveredScreenId];
    }
    return null;
  }
}