
<div
class="group relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
(mouseenter)="startSlideshow()"
(mouseleave)="stopSlideshow()"
>
<!-- Thumbnail Section -->
<div class="relative h-48 rounded-t-xl overflow-hidden">
  <!-- Slideshow Container -->
  <div class="relative w-full h-full">
    @for (item of playlist.items; track item.id) {
      <div 
        class="absolute inset-0 transition-opacity duration-500 flex items-center justify-center"
        [style.opacity]="currentIndex === playlist.items.indexOf(item) ? '1' : '0'"
      >
        @if (item.type === 'image' && item.content.url) {
          <img
            [src]="item.content.url"
            [alt]="item.name"
            class="w-full h-full object-cover"
          />
        } @else if (item.type === 'video' && item.content.thumbnail) {
          <div class="relative w-full h-full">
            <img
              [src]="item.content.thumbnail"
              [alt]="item.name"
              class="w-full h-full object-cover"
            />
            <div class="absolute bottom-2 right-2 px-2 py-1 bg-black/70 rounded text-white text-xs">
              {{ formatDuration(item.duration) }}
            </div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-12 h-12 bg-black/50 rounded-full flex items-center justify-center">
                <span class="material-icons text-white">play_arrow</span>
              </div>
            </div>
          </div>
        } @else {
          <div class="w-full h-full flex flex-col items-center justify-center bg-gray-100">
            <span class="material-icons text-4xl text-gray-400">{{ getItemIcon(item.type) }}</span>
            <p class="text-sm text-gray-500 mt-2 max-w-[80%] truncate px-2">{{ item.name }}</p>
          </div>
        }
      </div>
    }

    @if (!playlist.items.length) {
      <div class="w-full h-full flex flex-col items-center justify-center bg-gray-100">
        <span class="material-icons text-4xl text-gray-400">playlist_play</span>
        <p class="text-gray-500 mt-2">Empty playlist</p>
      </div>
    }

    <!-- Progress Indicators -->
    @if (playlist.items.length > 1) {
      <div class="absolute bottom-2 left-2 flex gap-1">
        @for (item of playlist.items; track item.id) {
          <div 
            class="w-2 h-2 rounded-full transition-all duration-300"
            [style.background-color]="currentIndex === playlist.items.indexOf(item) ? 'rgb(59, 130, 246)' : 'rgba(255, 255, 255, 0.5)'"
          ></div>
        }
      </div>
    }

    <!-- Item Counter -->
    @if (playlist.items.length > 1) {
      <div class="absolute top-2 right-2 px-2 py-1 bg-black/50 text-white text-xs rounded">
        {{ currentIndex + 1 }} / {{ playlist.items.length }}
      </div>
    }
  </div>

  <!-- Overlay Actions -->
  <div
    class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4"
  >
    <button
      (click)="preview.emit(playlist)"
      class="p-3 bg-white rounded-full hover:bg-blue-50 transition-all duration-300"
      title="Preview"
    >
      <span class="material-icons text-blue-600">play_arrow</span>
    </button>
    <button
      (click)="edit.emit(playlist)"
      class="p-3 bg-white rounded-full hover:bg-blue-50 transition-all duration-300"
      title="Edit"
    >
      <span class="material-icons text-blue-600">edit</span>
    </button>
    <button
      (click)="onDelete($event)"
      class="p-3 bg-white rounded-full hover:bg-red-50 transition-all duration-300"
      title="Delete"
    >
      <span class="material-icons text-red-600">delete</span>
    </button>
  </div>

  <!-- Status Badge -->
  <div class="absolute top-3 left-3">
    <span
      class="px-3 py-1 text-xs font-medium rounded-full text-white shadow-sm"
      [style.background-color]="getStatusBackgroundColor(playlist.status)"
    >
      {{ playlist.status }}
    </span>
  </div>

  <!-- Color Badge -->
  <div class="absolute top-3 left-20">
    <button
      class="w-6 h-6 rounded-full border-2 border-white shadow-sm block cursor-pointer hover:scale-110 transition-transform"
      [style.background-color]="getPlaylistColor()"
      title="Click to change playlist color"
      (click)="onColorClick($event)"
    ></button>
  </div>

  <!-- Duration Badge -->
  <div class="absolute top-3 right-3">
    <span class="px-3 py-1 text-xs font-medium bg-black/70 text-white rounded-full">
      {{ formatDuration(playlist.duration) }}
    </span>
  </div>
</div>

<!-- Content Section -->
<div class="p-5">
  <div class="flex justify-between items-start gap-2">
    <h3 class="font-medium text-gray-900 line-clamp-2 flex-1">
      {{ playlist.name }}
    </h3>
  </div>
  
  <div class="grid grid-cols-2 gap-3 mt-4">
    <!-- Items Count -->
    <div class="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
      <span class="material-icons text-blue-500 text-sm">video_library</span>
      <div>
        <p class="text-xs text-gray-500">Items</p>
        <p class="text-sm font-medium">{{ playlist.items.length }}</p>
      </div>
    </div>

    <!-- Last Modified -->
    <div class="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
      <span class="material-icons text-gray-500 text-sm">update</span>
      <div>
        <p class="text-xs text-gray-500">Updated</p>
        <p class="text-sm font-medium">{{ getTimeAgo(playlist.lastModified) }}</p>
      </div>
    </div>
  </div>
</div>

<!-- Color Picker Modal -->
@if (colorPickerModal.show) {
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Choose Playlist Color</h3>
        <button (click)="closeColorPicker()" class="text-gray-400 hover:text-gray-600">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-3">{{ playlist.name }}</p>
        
        <!-- Predefined Colors -->
        <div class="grid grid-cols-8 gap-2 mb-4">
          @for (color of predefinedColors; track color) {
            <div class="w-8 h-8 rounded-full cursor-pointer border-2 border-gray-200 hover:border-gray-400 transition-colors"
              [style.background-color]="color"
              [class.ring-2]="colorPickerModal.selectedColor === color"
              [class.ring-blue-500]="colorPickerModal.selectedColor === color"
              (click)="selectColor(color)">
            </div>
          }
        </div>
        
        <!-- Custom Color Input -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Custom Color</label>
          <div class="flex items-center space-x-2">
            <input type="color" 
              [(ngModel)]="colorPickerModal.selectedColor"
              class="w-12 h-8 rounded border border-gray-300 cursor-pointer">
            <input type="text" 
              [(ngModel)]="colorPickerModal.selectedColor"
              placeholder="#3B82F6"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>
        
        <!-- Preview -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
          <div class="flex items-center p-3 bg-gray-50 rounded-md">
            <div class="w-4 h-4 rounded-full mr-3 border-2 border-white shadow-sm"
              [style.background-color]="colorPickerModal.selectedColor">
            </div>
            <span class="text-sm text-gray-700">{{ playlist.name }}</span>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end space-x-3">
        <button (click)="closeColorPicker()" 
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
          Cancel
        </button>
        <button (click)="savePlaylistColor()" 
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
          Save Color
        </button>
      </div>
    </div>
  </div>
}
</div>