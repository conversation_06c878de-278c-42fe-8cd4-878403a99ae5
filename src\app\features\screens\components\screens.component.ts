import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Screen, CreateScreenDto, ScreenFilters } from '../../../models/screen.model';
import { ScreenService } from '../services/screen.service';
import { AreaService } from '../../../features/area/services/area.service';
import { Area } from '../../../models/area.model';
import { ScreenCardComponent } from './screen-card/screen-card.component';
import { ScreenTableComponent } from './screen-table/screen-table.component';
import { CreateScreenDialogComponent } from './create-screen-dialog/create-screen-dialog.component';

interface ScreenStats {
  total: number;
  online: number;
  offline: number;
  maintenance: number;
  error: number;
}

@Component({
  selector: 'app-screens',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ScreenCardComponent,
    ScreenTableComponent,
    CreateScreenDialogComponent,
  ],
  templateUrl: './screens.component.html',
})
export class ScreensComponent implements OnInit, OnDestroy {
  screens: Screen[] = [];
  filteredScreens: Screen[] = [];
  loading = true;
  viewMode: 'grid' | 'list' = 'grid';
  searchQuery = '';
  showCreateDialog = false;
  selectedScreen: Screen | null = null;
  private heartbeatInterval: any;

  // Channels and areas data
  channels = [
    { id: 'ch1', name: 'Main Channel' },
    { id: 'ch2', name: 'Secondary Channel' },
  ];
  
  areas: Area[] = [];

  filters: ScreenFilters = {
    areaId: '',
    status: '',
  };

  constructor(
    private screenService: ScreenService,
    private areaService: AreaService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAreas();
    this.loadScreens();
    // Check heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      this.checkHeartbeats();
    }, 30000);
  }

  ngOnDestroy(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
  }

  get screensByStatus(): ScreenStats {
    return {
      total: this.screens.length,
      online: this.screens.filter(s => s.status === 'online').length,
      offline: this.screens.filter(s => s.status === 'offline').length,
      maintenance: this.screens.filter(s => s.status === 'maintenance').length,
      error: this.screens.filter(s => s.status === 'error').length,
    };
  }

  loadAreas(): void {
    this.areaService.getAreas().subscribe({
      next: (areas) => {
        // Flatten the areas to get a simple list with id and name
        this.areas = areas.map(area => ({
          id: area.id,
          name: area.name
        } as Area));
        console.log('Loaded areas:', this.areas); // Add this for debugging
      },
      error: (error) => {
        console.error('Error loading areas:', error);
      }
    });
  }

  loadScreens(): void {
    this.loading = true;
    this.screenService.getScreens().subscribe({
      next: (screens) => {
        this.screens = screens;
        this.checkHeartbeats(); // Check heartbeats immediately after loading
        this.loading = false;
        this.applyFilters(); // Apply filters after loading
      },
      error: (error) => {
        console.error('Error loading screens:', error);
        this.loading = false;
      },
    });
  }

  checkHeartbeats(): void {
    const now = new Date();
    const updatedScreens = this.screens.map(screen => {
      // If last_ping is more than 2 minutes ago, mark as offline
      if (screen.last_ping) {
        const lastPing = new Date(screen.last_ping);
        const diffMinutes = (now.getTime() - lastPing.getTime()) / (1000 * 60);
        
        // If more than 2 minutes since last ping, mark as offline
        if (diffMinutes > 2) {
          return { ...screen, status: 'offline' as const };
        }
      } else {
        // If no last_ping, mark as offline
        return { ...screen, status: 'offline' as const };
      }
      
      return screen;
    });
    
    this.screens = updatedScreens as Screen[];
    this.applyFilters();
  }

  applyFilters(): void {
    const searchTerm = this.searchQuery.toLowerCase();
    console.log('Applying filters:', { searchTerm, filters: this.filters, allScreens: this.screens.length }); // Add debugging
    
    this.filteredScreens = this.screens.filter((screen) => {
      const matchesSearch =
        !searchTerm ||
        screen.name.toLowerCase().includes(searchTerm) ||
        screen.channel_name.toLowerCase().includes(searchTerm);

      const matchesArea = !this.filters.areaId || screen.area_id === this.filters.areaId;
      const matchesStatus = !this.filters.status || screen.status === this.filters.status;
      
      console.log('Screen filter check:', { 
        screenId: screen.id, 
        screenName: screen.name, 
        screenAreaId: screen.area_id,
        filterAreaId: this.filters.areaId,
        matchesArea,
        matchesStatus,
        matchesSearch
      }); // Add debugging

      return matchesSearch && matchesArea && matchesStatus;
    });
    
    console.log('Filtered screens count:', this.filteredScreens.length); // Add debugging
  }

  openCreateDialog(): void {
    this.showCreateDialog = true;
  }

  closeCreateDialog(): void {
    this.showCreateDialog = false;
  }

  handleCreateScreen(): void {
    this.closeCreateDialog();
    this.loadScreens();
  }

  editScreen(screen: Screen): void {
    // Implement edit functionality
    console.log('Edit screen:', screen);
  }

  viewScreenDetails(screen: Screen): void {
    console.log('Navigating to screen details:', screen.id);
    this.router.navigate(['/screens', screen.id]).then(
      (success) => {
        console.log('Navigation successful:', success);
      },
      (error) => {
        console.error('Navigation failed:', error);
      }
    );
  }

  confirmDelete(screen: Screen): void {
    const confirmMessage = `Are you sure you want to delete "${screen.name}"?

This action cannot be undone and will permanently remove the screen and all its data.`;
    
    if (confirm(confirmMessage)) {
      this.screenService.deleteScreen(screen.id).subscribe({
        next: () => {
          console.log(`Screen "${screen.name}" deleted successfully`);
          this.screens = this.screens.filter((s) => s.id !== screen.id);
          this.applyFilters();
          
          // Show success feedback
          // Using a more modern approach instead of alert
          this.showNotification(`Screen "${screen.name}" has been deleted successfully.`, 'success');
        },
        error: (error) => {
          console.error('Error deleting screen:', error);
          this.showNotification(`Failed to delete screen "${screen.name}". Please try again.`, 'error');
        },
      });
    }
  }

  // Add a method to handle delete by ID (for compatibility with components that pass ID only)
  confirmDeleteById(screenId: string): void {
    const screen = this.screens.find(s => s.id === screenId);
    if (screen) {
      this.confirmDelete(screen);
    }
  }

  

  // Add a method to show notifications
  showNotification(message: string, type: 'success' | 'error' | 'info'): void {
    // For now, we'll just use alert, but in a real app you'd want a proper notification system
    alert(message);
    
    // In a real implementation, you might have something like:
    // this.notificationService.show(message, type);
  }

  // Add a method to handle filter changes
  onFilterChange(): void {
    console.log('Filter changed:', this.filters); // Add debugging
    this.applyFilters();
  }

  // Add a method to handle search changes
  onSearchChange(): void {
    this.applyFilters();
  }
}