<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="max-w-7xl mx-auto bg-white rounded-xl shadow-sm p-6 mb-6">
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">Media Library</h1>
          <p class="mt-1 text-sm text-gray-500">
            Manage your media assets for digital signage
          </p>
        </div>

        <div class="flex flex-col sm:flex-row w-full md:w-auto gap-3">
          <!-- Search -->
          <div class="relative flex-grow sm:max-w-xs">
            <input
              type="text"
              [(ngModel)]="searchQuery"
              (ngModelChange)="applyFilters()"
              placeholder="Search media..."
              class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-200 transition-colors"
            />
            <span class="material-icons absolute left-3 top-2.5 text-gray-400 text-sm">
              search
            </span>
          </div>

          <!-- Upload Button -->
          <button
            (click)="showUploadDialog = true"
            class="flex items-center justify-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium shadow-lg"
          >
            <span class="material-icons text-sm mr-2">upload</span>
            Upload Media
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="max-w-7xl mx-auto mb-6">
      <app-media-filter
        [filters]="filters"
        (filterChange)="onFilterChange($event)"
      />
    </div>

    <!-- View Toggle and Stats -->
    <div class="max-w-7xl mx-auto mb-6 flex justify-between items-center">
      <div class="text-xl text-gray-500 p-2">
        {{ mediaItems.length }} items
      </div>
      <div class="flex items-center space-x-2">
        <button
          (click)="viewMode = 'grid'"
          class="p-2 rounded-lg"
          [class.bg-blue-50]="viewMode === 'grid'"
          [class.text-blue-600]="viewMode === 'grid'"
        >
          <span class="material-icons">grid_view</span>
        </button>
        <button
          (click)="viewMode = 'list'"
          class="p-2 rounded-lg"
          [class.bg-blue-50]="viewMode === 'list'"
          [class.text-blue-600]="viewMode === 'list'"
        >
          <span class="material-icons">view_list</span>
        </button>
      </div>
    </div>

    <!-- Loading State -->
    @if (loading) {
      <div class="max-w-7xl mx-auto">
        <div class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    }

    <!-- Error State -->
    @if (error) {
      <div class="max-w-7xl mx-auto">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <span class="material-icons text-red-500 mr-2">error</span>
          <p class="text-red-700">{{ error }}</p>
        </div>
      </div>
    }

    <!-- Media Content -->
    @if (!loading && !error) {
      <div class="max-w-7xl mx-auto">
        @if (mediaItems.length === 0) {
          <div class="bg-white rounded-xl p-8 text-center">
            <span class="material-icons text-4xl text-gray-400 mb-2">photo_library</span>
            <h3 class="text-lg font-medium text-gray-900 mb-1">No media found</h3>
            <p class="text-gray-500">Upload media files to get started</p>
          </div>
        } @else {
          @if (viewMode === 'grid') {
            <app-media-grid
              [media]="mediaItems"
              (select)="onMediaSelect($event)"
              (delete)="onMediaDelete($event)"
            />
          } @else {
            <app-media-list
              [media]="mediaItems"
              (select)="onMediaSelect($event)"
              (delete)="onMediaDelete($event)"
            />
          }
        }
      </div>
    }

    <!-- Upload Dialog -->
    @if (showUploadDialog) {
      <app-media-upload
        (uploadComplete)="onUploadComplete($event)"
        (cancel)="showUploadDialog = false"
      />
    }

    @if (selectedMedia) {
      <app-media-preview-dialog
        [media]="selectedMedia"
        (close)="selectedMedia = null"
      />
    }
  </div>