import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Observable } from 'rxjs';
import { Media } from '../../../models/media.model';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-media-library',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './media-library.component.html',
  styleUrls: ['./media-library.component.scss']
})
export class MediaLibraryComponent {
  @Input() media$!: Observable<Media[]>;
  @Output() mediaDragStart = new EventEmitter<{ event: DragEvent, media: Media }>();
  @Output() mediaDragEnd = new EventEmitter<DragEvent>();
  @Output() addMediaToPlaylist = new EventEmitter<Media>();

  onMediaDragStart(event: DragEvent, media: Media) {
    console.log('Drag started for media:', media);
    
    // Set the drag effect
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copy';
      
      // Store the media data as JSON in the drag event
      event.dataTransfer.setData('text/plain', JSON.stringify(media));
      
      // Set a custom drag image
      const dragImage = document.createElement('div');
      dragImage.className = 'drag-image';
      dragImage.innerHTML = `
        <div class="flex items-center">
          <span class="material-icons drag-image-icon mr-2">image</span>
          <span class="truncate max-w-xs">${media.name}</span>
        </div>
      `;
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px'; // Move off-screen
      dragImage.style.backgroundColor = '#3b82f6'; // blue-500
      dragImage.style.color = 'white';
      dragImage.style.padding = '8px 12px';
      dragImage.style.borderRadius = '0.5rem';
      dragImage.style.fontSize = '0.875rem';
      dragImage.style.fontWeight = '500';
      dragImage.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
      dragImage.style.pointerEvents = 'none';
      dragImage.style.zIndex = '9999';
      dragImage.style.maxWidth = '200px';
      dragImage.style.display = 'flex';
      dragImage.style.alignItems = 'center';
      dragImage.style.justifyContent = 'center';
      document.body.appendChild(dragImage);
      event.dataTransfer.setDragImage(dragImage, 0, 0);
      
      // Add visual feedback to the dragged element
      const target = event.target as HTMLElement;
      if (target) {
        target.classList.add('media-item-dragging');
      }
      
      // Remove the temporary drag image after a short delay
      setTimeout(() => {
        if (dragImage.parentNode) {
          document.body.removeChild(dragImage);
        }
      }, 0);
    }
    
    this.mediaDragStart.emit({ event, media });
  }

  onMediaDragEnd(event: DragEvent) {
    console.log('Drag ended');
    // Clean up any visual effects if needed
    const target = event.target as HTMLElement;
    if (target) {
      target.classList.remove('media-item-dragging');
    }
    
    this.mediaDragEnd.emit(event);
  }

  addToPlaylist(media: Media) {
    this.addMediaToPlaylist.emit(media);
  }
}
