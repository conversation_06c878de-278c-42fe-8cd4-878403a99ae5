<div class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4">
      <div class="p-6 border-b">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-semibold">Upload Media</h2>
          <button
            (click)="cancel.emit()"
            class="text-gray-400 hover:text-gray-600"
            [disabled]="isUploading"
          >
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
  
      <div class="p-6">
        <div
          class="border-2 border-dashed border-gray-200 rounded-xl p-8 text-center transition-colors duration-200"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onDrop($event)"
          [class.opacity-50]="isUploading"
        >
          <span class="material-icons text-4xl text-gray-400 mb-2">cloud_upload</span>
          <h3 class="text-lg font-medium mb-1">Drag and drop files here</h3>
          <p class="text-sm text-gray-500 mb-4">or</p>
          <input
            type="file"
            multiple
            accept="image/*,video/*"
            class="hidden"
            #fileInput
            (change)="onFileSelected($event)"
            [disabled]="isUploading"
          />
          <button
            (click)="fileInput.click()"
            class="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="isUploading"
          >
            Browse Files
          </button>
        </div>
  
        @if (isUploading) {
          <div class="mt-4">
            <!-- Upload Progress Header -->
            <div class="flex justify-between items-center text-sm mb-2">
              <div class="flex items-center space-x-2">
                <div class="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                <span class="font-medium">Uploading files...</span>
              </div>
              <span class="text-gray-600">{{ uploadProgress }}%</span>
            </div>
            
            <!-- File Progress Info -->
            @if (totalFiles > 0) {
              <div class="text-xs text-gray-500 mb-2">
                <div class="flex justify-between">
                  <span>{{ filesCompleted }} of {{ totalFiles }} files completed</span>
                  @if (currentFileName) {
                    <span class="max-w-xs truncate">{{ currentFileName }}</span>
                  }
                </div>
              </div>
            }
            
            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out relative"
                [style.width.%]="uploadProgress"
              >
                <!-- Animated shimmer effect -->
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
              </div>
            </div>
            
            <!-- Success indicator -->
            @if (successfulUploads.length > 0) {
              <div class="mt-2 text-xs text-green-600">
                ✓ {{ successfulUploads.length }} file(s) uploaded successfully
              </div>
            }
          </div>
        }
  
        @if (error) {
          <div class="mt-4 p-4 bg-red-50 text-red-600 rounded-lg">
            {{ error }}
          </div>
        }
      </div>
    </div>
  </div>