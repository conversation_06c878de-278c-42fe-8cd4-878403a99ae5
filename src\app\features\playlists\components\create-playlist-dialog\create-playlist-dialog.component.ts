// create-playlist-dialog.component.ts
import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PlaylistService } from '../../services/playlist.service';

@Component({
  selector: 'app-create-playlist-dialog',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './create-playlist-dialog.component.html',
})
export class CreatePlaylistDialogComponent {
  @Output() onCreate = new EventEmitter<void>();
  @Output() onCancel = new EventEmitter<void>();

  playlistForm: FormGroup;
  isSubmitting = false;
  selectedColor = '#3B82F6'; // Default blue color

  // Predefined color options
  colorOptions = [
    '#3B82F6', // blue-500
    '#10B981', // green-500
    '#F59E0B', // amber-500
    '#EF4444', // red-500
    '#8B5CF6', // violet-500
    '#EC4899', // pink-500
    '#06B6D4', // cyan-500
    '#84CC16', // lime-500
  ];

  constructor(
    private fb: FormBuilder,
    private playlistService: PlaylistService
  ) {
    this.playlistForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      duration: [10],
      visibility: ['public'],
      autoPlay: [true],
    });
  }

  selectColor(color: string): void {
    this.selectedColor = color;
  }

  handleSubmit(): void {
    if (this.playlistForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      this.playlistService
        .createPlaylist({
          ...this.playlistForm.value,
          color: this.selectedColor,
          settings: {
            autoPlay: this.playlistForm.value.autoPlay,
            defaultDuration: this.playlistForm.value.duration,
            loop: false,
            defaultMuted: true,
            transition: { type: 'fade', duration: 0.5 },
            scheduling: { enabled: false, priority: 1 },
          },
        })
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            this.onCreate.emit();
          },
          error: (error) => {
            console.error('Error creating playlist:', error);
            this.isSubmitting = false;
          },
        });
    }
  }
}
