import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface PriorityOption {
  value: number;
  label: string;
  description: string;
}

interface TimeErrors {
  start_time?: string;
  end_time?: string;
  priority?: string;
  duration?: string;
  time_conflict?: string;
}

@Component({
  selector: 'app-step-time-configuration',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="space-y-8">
      <!-- Step Header -->
      <div class="text-center">
        <h3 class="text-xl font-light text-gray-900 mb-2">Set Time Range</h3>
        <p class="text-gray-500 text-sm">When should this playlist be displayed?</p>
      </div>

      <!-- Time Range Configuration -->
      <div class="space-y-6">
        <div class="grid grid-cols-2 gap-4">
          <!-- Start Time -->
          <div>
            <label class="block text-sm font-medium text-gray-900 mb-3">
              Start Time
            </label>
            <div class="relative">
              <input
                type="time"
                [(ngModel)]="localStartTime"
                (ngModelChange)="onStartTimeChange($event)"
                class="w-full px-4 py-3 border-0 bg-gray-50 rounded-xl focus:bg-white focus:ring-2 focus:ring-gray-900 text-sm transition-all duration-200"
              />
            </div>
          </div>

          <!-- End Time -->
          <div>
            <label class="block text-sm font-medium text-gray-900 mb-3">
              End Time
            </label>
            <div class="relative">
              <input
                type="time"
                [(ngModel)]="localEndTime"
                (ngModelChange)="onEndTimeChange($event)"
                class="w-full px-4 py-3 border-0 bg-gray-50 rounded-xl focus:bg-white focus:ring-2 focus:ring-gray-900 text-sm transition-all duration-200"
              />
            </div>
          </div>
        </div>

        <!-- Duration Display -->
        @if (localStartTime && localEndTime && isValidTimeRange()) {
          <div class="text-center py-4">
            <div class="inline-flex items-center px-4 py-2 bg-gray-50 rounded-xl">
              <span class="material-icons text-gray-600 mr-2 text-lg">schedule</span>
              <span class="text-sm text-gray-700 font-medium">
                Duration: {{ calculateDuration() }}
              </span>
            </div>
          </div>
        }
      </div>

      <!-- Priority Configuration -->
      <div class="space-y-4">
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-2">Priority</h4>
          <p class="text-xs text-gray-500 mb-4">
            Higher priority schedules override lower ones during conflicts
          </p>
        </div>

        <div class="space-y-2">
          @for (option of priorityOptions; track option.value) {
            <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-xl transition-all duration-200 hover:bg-gray-50"
                   [class.bg-gray-50]="localPriority === option.value"
                   [class.border]="localPriority === option.value"
                   [class.border-gray-200]="localPriority === option.value"
>
              <input
                type="radio"
                [value]="option.value"
                [(ngModel)]="localPriority"
                (ngModelChange)="onPriorityChange($event)"
                class="text-gray-900 focus:ring-gray-900 focus:ring-2"
              />
              <div class="flex-1">
                <div class="flex items-center justify-between">
                  <span class="font-medium text-gray-900 text-sm">{{ option.label }}</span>
                  <span class="text-xs text-gray-500 font-medium">{{ option.value }}</span>
                </div>
                <p class="text-xs text-gray-600 mt-1">{{ option.description }}</p>
              </div>
            </label>
          }
        </div>

      </div>

      <!-- Quick Time Presets -->
      <div class="bg-gray-50 rounded-lg p-6">
        <h4 class="font-medium text-gray-900 mb-4">Quick Presets</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          @for (preset of timePresets; track preset.label) {
            <button
              type="button"
              (click)="applyTimePreset(preset)"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-white hover:border-blue-300 transition-colors text-center"
            >
              <div class="font-medium text-gray-900">{{ preset.label }}</div>
              <div class="text-xs text-gray-600">{{ preset.start }} - {{ preset.end }}</div>
            </button>
          }
        </div>
      </div>

      <!-- Time Conflict Warning -->
      @if (hasTimeConflict) {
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-start">
            <span class="material-icons text-yellow-600 mr-3 mt-0.5">warning</span>
            <div>
              <h4 class="font-medium text-yellow-800">Potential Schedule Conflict</h4>
              <p class="text-sm text-yellow-700 mt-1">
                This time range may overlap with existing schedules. The priority setting will determine which playlist displays.
              </p>
            </div>
          </div>
        </div>
      }

      <!-- Current Configuration Summary -->
      @if (localStartTime && localEndTime && isValidTimeRange()) {
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-center">
            <span class="material-icons text-green-600 mr-2">check_circle</span>
            <div>
              <h4 class="font-medium text-green-900">Time Configuration Set</h4>
              <p class="text-sm text-green-700">
                {{ formatTime(localStartTime) }} to {{ formatTime(localEndTime) }} 
                ({{ calculateDuration() }}) • {{ getPriorityLabel(localPriority) }}
              </p>
            </div>
          </div>
        </div>
      }
    </div>
  `
})
export class StepTimeConfigurationComponent implements OnInit {
  @Input() startTime: string = '';
  @Input() endTime: string = '';
  @Input() priority: number = 2;
  @Input() priorityOptions: PriorityOption[] = [];
  @Input() errors: TimeErrors | undefined;
  @Output() timeConfigured = new EventEmitter<{ start_time: string; end_time: string; priority: number }>();

  localStartTime: string = '';
  localEndTime: string = '';
  localPriority: number = 2;
  hasTimeConflict = false;

  timePresets = [
    { label: 'Morning', start: '08:00', end: '12:00' },
    { label: 'Afternoon', start: '12:00', end: '17:00' },
    { label: 'Evening', start: '17:00', end: '22:00' },
    { label: 'All Day', start: '00:00', end: '23:59' },
    { label: 'Business Hours', start: '09:00', end: '17:00' },
    { label: 'Lunch Time', start: '12:00', end: '13:00' },
    { label: 'After Hours', start: '18:00', end: '08:00' },
    { label: 'Weekend', start: '10:00', end: '20:00' }
  ];

  ngOnInit(): void {
    this.localStartTime = this.startTime;
    this.localEndTime = this.endTime;
    this.localPriority = this.priority;
  }

  ngOnChanges(): void {
    this.localStartTime = this.startTime;
    this.localEndTime = this.endTime;
    this.localPriority = this.priority;
  }

  onStartTimeChange(time: string): void {
    this.localStartTime = time;
    this.emitTimeConfiguration();
    this.checkForConflicts();
  }

  onEndTimeChange(time: string): void {
    this.localEndTime = time;
    this.emitTimeConfiguration();
    this.checkForConflicts();
  }

  onPriorityChange(priority: number): void {
    this.localPriority = priority;
    this.emitTimeConfiguration();
  }

  applyTimePreset(preset: { label: string; start: string; end: string }): void {
    this.localStartTime = preset.start;
    this.localEndTime = preset.end;
    this.emitTimeConfiguration();
    this.checkForConflicts();
  }

  private emitTimeConfiguration(): void {
    // Always emit the current values, even if invalid, so parent can validate
    this.timeConfigured.emit({
      start_time: this.localStartTime,
      end_time: this.localEndTime,
      priority: this.localPriority
    });
  }

  isValidTimeRange(): boolean {
    if (!this.localStartTime || !this.localEndTime) return false;
    return this.localStartTime < this.localEndTime;
  }

  calculateDuration(): string {
    if (!this.isValidTimeRange()) return '';

    const start = new Date(`1970-01-01T${this.localStartTime}:00`);
    const end = new Date(`1970-01-01T${this.localEndTime}:00`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    const diffMinutes = (diffMs % (1000 * 60 * 60)) / (1000 * 60);

    if (diffHours >= 1) {
      return diffMinutes > 0 
        ? `${Math.floor(diffHours)}h ${Math.floor(diffMinutes)}m`
        : `${Math.floor(diffHours)}h`;
    } else {
      return `${Math.floor(diffMinutes)}m`;
    }
  }

  formatTime(time: string): string {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  getPriorityBadgeClass(priority: number): string {
    switch (priority) {
      case 1:
        return 'bg-red-100 text-red-800';
      case 2:
        return 'bg-yellow-100 text-yellow-800';
      case 3:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPriorityLabel(priority: number): string {
    const option = this.priorityOptions.find(p => p.value === priority);
    return option ? option.label : `Priority ${priority}`;
  }

  private checkForConflicts(): void {
    // TODO: Implement actual conflict checking with existing schedules
    // For now, just simulate potential conflicts
    this.hasTimeConflict = false;
  }
}
