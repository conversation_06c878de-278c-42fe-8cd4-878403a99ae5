.screen-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-row {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #f9fafb;
  }
  
  &.selected {
    background-color: #eff6ff;
    border-left: 4px solid #3b82f6;
  }
  
  &.online-status {
    background-color: #f0fdf4;
    border-left: 4px solid #22c55e;
  }
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &.online { background-color: #22c55e; }
  &.active { background-color: #3b82f6; }
  &.critical { background-color: #ef4444; }
  &.alert { background-color: #f59e0b; }
  &.inactive { background-color: #6b7280; }
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  
  &.online {
    background-color: #dcfce7;
    color: #166534;
  }
  
  &.active {
    background-color: #dbeafe;
    color: #1e40af;
  }
  
  &.critical {
    background-color: #fee2e2;
    color: #991b1b;
  }
  
  &.alert {
    background-color: #fef3c7;
    color: #92400e;
  }
  
  &.inactive {
    background-color: #f3f4f6;
    color: #374151;
  }
}

.filter-button {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &.active {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
  }
}

.action-button {
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #f3f4f6;
    transform: scale(1.05);
  }
}

.header-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.stats-section {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.table-header {
  background-color: #ffffff;
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  
  .icon-container {
    width: 3rem;
    height: 3rem;
    background-color: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
  }
}

.footer-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
}

// Responsive adjustments
@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  
  .col-span-2 {
    grid-column: span 1 / span 1;
  }
}

// Animation for row selection
@keyframes selectRow {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.table-row.selecting {
  animation: selectRow 0.3s ease-in-out;
}

// Screen Preview Modal
.screen-preview-modal {
  width: 380px;
  box-shadow: 0 25px 30px -5px rgba(0, 0, 0, 0.4), 0 15px 15px -5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  overflow: hidden;
  
  .screen-frame {
    background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
    border-radius: 8px;
    padding: 4px;
    position: relative;
    margin: 0 6px;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 8px;
      box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.8);
      pointer-events: none;
      z-index: 1;
    }
  }
  
  .screen-content {
    background: #000;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
  }
}

// Custom scrollbar
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}