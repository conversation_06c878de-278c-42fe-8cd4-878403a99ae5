<!-- //admin dashboard -->

<div class="min-h-screen bg-gray-50">
  <!-- Header Section -->
  <div class="max-w-7xl mx-auto bg-white rounded-xl shadow-sm p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">Screens</h1>
        <p class="mt-1 text-sm text-gray-500">Manage your display devices</p>
      </div>

      <div class="flex flex-col sm:flex-row w-full md:w-auto gap-3">
        <!-- Search -->
        <div class="relative flex-grow sm:max-w-xs">
          <input
            type="text"
            [(ngModel)]="searchQuery"
            (ngModelChange)="onSearchChange()"
            placeholder="Search screens..."
            class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-200 transition-colors"
          />
          <span class="material-icons absolute left-3 top-2.5 text-gray-400 text-sm">search</span>
        </div>

        <!-- Add Screen Button -->
        <button
          (click)="openCreateDialog()"
          class="flex items-center justify-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium shadow-lg"
        >
          <span class="material-icons text-sm mr-2">add</span>
          Add Screen
        </button>
      </div>
    </div>
  </div>


  <!-- Filters -->
  <div class="max-w-7xl mx-auto mb-6">
    <div class="bg-white rounded-xl p-4 shadow-sm">
      <div class="flex flex-wrap gap-4">
        <!-- Area Filter -->
        <div class="flex-grow min-w-[200px]">
          <label class="block text-sm font-medium text-gray-700 mb-1">Area</label>
          <select
            [(ngModel)]="filters.areaId"
            (ngModelChange)="onFilterChange()"
            class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-100"
          >
            <option value="">All Areas</option>
            @for (area of areas; track area.id) {
              <option [value]="area.id">{{ area.name }}</option>
            }
          </select>
        </div>

        <!-- Status Filter -->
        <div class="flex-grow min-w-[200px]">
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            [(ngModel)]="filters.status"
            (ngModelChange)="onFilterChange()"
            class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-100"
          >
            <option value="">All Statuses</option>
            <option value="online">Online</option>
            <option value="offline">Offline</option>
            <option value="maintenance">Maintenance</option>
            <option value="error">Error</option>
          </select>
        </div>

        <!-- View Toggle -->
        <div class="flex items-end">
          <div class="flex bg-gray-50 rounded-lg p-1">
            <button
              (click)="viewMode = 'grid'"
              class="p-2 rounded-lg transition-colors"
              [ngClass]="viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-500'"
            >
              <span class="material-icons">grid_view</span>
            </button>
            <button
              (click)="viewMode = 'list'"
              class="p-2 rounded-lg transition-colors"
              [ngClass]="viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-500'"
            >
              <span class="material-icons">view_list</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Screen List -->
  <div class="max-w-7xl mx-auto px-6">

    @if (loading) {
      <div class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    } @else if (filteredScreens.length === 0) {
      <div class="bg-white rounded-xl p-8 text-center shadow-sm">
        <span class="material-icons text-gray-400 text-4xl mb-2">search_off</span>
        <h3 class="text-lg font-medium text-gray-900 mb-1">No screens found</h3>
        <p class="text-gray-500">Try adjusting your search criteria</p>
      </div>
    } @else {
      @if (viewMode === 'grid') {
        <div class="grid gap-6" 
             style="grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));">
          @for (screen of filteredScreens; track screen.id) {
            <app-screen-card
              [screen]="screen"
              (edit)="editScreen($event)"
              (viewDetails)="viewScreenDetails($event)"
              (delete)="confirmDelete($event)"
            />
          }
        </div>
      } @else {
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
          <app-screen-table
            [screens]="filteredScreens"
            (edit)="editScreen($event)"
            (delete)="confirmDelete($event)"
          />
        </div>
      }
    }
  </div>

  <!-- Dialogs -->
  @if (showCreateDialog) {
    <app-create-screen-dialog
      (onCreate)="handleCreateScreen()"
      (onCancel)="closeCreateDialog()"
    />
  }

  <!-- @if (showEditDialog && selectedScreen) {
    <app-edit-screen-dialog
      [screen]="selectedScreen"
      [channels]="channels"
      [areas]="areas"
      (onSave)="handleUpdateScreen($event)"
      (onCancel)="closeEditDialog()"
    />
  }

  @if (showDeleteDialog && selectedScreen) {
    <app-confirm-dialog
      title="Delete Screen"
      [message]="'Are you sure you want to delete ' + selectedScreen.name + '? This action cannot be undone.'"
      confirmText="Delete"
      cancelText="Cancel"
      (onConfirm)="handleDeleteScreen()"
      (onCancel)="closeDeleteDialog()"
    />
  } -->
</div>