<div 
  class="tv-display"
  [class.portrait]="isPortrait"
  [class.landscape]="!isPortrait"
  [class.compact]="compact"
  [class.has-error]="!!error"
  [class.offline]="!isPlaying && !currentPlaylist"
>
  <div class="tv-frame">
    <div class="tv-screen">
      @if (error) {
        <div class="error-state">
          <span class="material-icons text-red-500">error_outline</span>
          <p class="error-message">{{ error }}</p>
          <button 
            *ngIf="currentPlaylist?.items?.length"
            (click)="initializePlayback(); $event.stopPropagation()"
            class="retry-button"
          >
            Retry
          </button>
        </div>
      } @else if (currentPlaylist && isPlaying) {
        <div class="playlist-display">
          <div class="media-container">
            @if (currentItem) {
              @switch (currentItem.type) {
                @case ('image') {
                  <img 
                    [src]="currentItem.content.url" 
                    [alt]="currentItem.name"
                    class="media-content"
                  />
                }
                @case ('video') {
                  <video 
                    [src]="currentItem.content.url"
                    class="media-content"
                    autoplay
                    [muted]="currentItem.settings.muted !== false"
                    [loop]="currentItem.settings.loop === true"
                  ></video>
                }
                @case ('webpage') {
                  <iframe 
                    [src]="currentItem.content.url"
                    class="media-content"
                    frameborder="0"
                  ></iframe>
                }
                @case ('ticker') {
                  <div class="ticker-content">
                    <div class="ticker-text">{{ currentItem.name }}</div>
                  </div>
                }
                @default {
                  <div class="placeholder-content">
                    <span class="material-icons">play_circle_filled</span>
                    <p>{{ currentItem.name }}</p>
                  </div>
                }
              }
              
              <!-- Progress bar for current item -->
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  [style.width.%]="playbackProgress"
                ></div>
              </div>
            } @else {
              <div class="loading-content">
                <div class="spinner"></div>
                <p>Loading media...</p>
              </div>
            }
          </div>
          
          <!-- Playlist info overlay -->
          <div class="playlist-info">
            <div class="playlist-name">{{ currentPlaylist.name }}</div>
            <div class="item-info mt-1">
              <span class="font-bold">{{ currentItemIndex + 1 }}/{{ currentPlaylist.items.length }}</span>
              <span class="mx-2 font-bold">•</span>
              <span class="truncate font-medium">{{ currentItem?.name || 'Loading...' }}</span>
            </div>
          </div>
        </div>
      } @else if (currentPlaylist) {
        <div class="no-content">
          <span class="material-icons">play_circle_outline</span>
          <p>Playlist Ready</p>
          @if (currentPlaylist.items && currentPlaylist.items.length > 0) {
            <div class="playlist-preview">
              <p class="text-xs text-gray-500 mt-1">{{ currentPlaylist.name }}</p>
              <p class="text-[0.6rem] text-gray-400">{{ currentPlaylist.items.length }} items</p>
            </div>
          } @else {
            <p class="text-xs text-gray-500 mt-2">Playlist is empty</p>
          }
        </div>
      } @else if (!isPlaying) {
        <div class="offline-content">
          <span class="material-icons text-gray-400">cloud_off</span>
          <p class="text-gray-500 font-medium">OFFLINE</p>
          <p class="text-xs text-gray-400 mt-1">Screen is not connected</p>
        </div>
      } @else {
        <div class="no-content">
          <span class="material-icons">tv_off</span>
          <p>No playlist assigned</p>
        </div>
      }
    </div>
    <div class="tv-stand"></div>
  </div>
</div>