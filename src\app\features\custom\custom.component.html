<div class="h-screen flex flex-col bg-gray-50">
  <!-- Header -->
  <header class="bg-white border-b border-gray-200 px-6 py-4">
    <div *ngIf="!isFullscreen" class="flex justify-between items-center">
      <h1 class="text-xl font-semibold text-gray-800">Custom Page</h1>
      <div class="flex items-center space-x-4">
        <button class="flex items-center text-sm text-gray-600 hover:text-gray-900">
          <span class="material-icons mr-1 text-lg">save</span>
          Save
        </button>
        <button (click)="toggleFullscreen()" class="flex items-center text-sm text-gray-600 hover:text-gray-900">
          <span class="material-icons mr-1 text-lg">fullscreen</span>
          Fullscreen
        </button>
      </div>
    </div>
    <div *ngIf="isFullscreen" class="flex justify-between items-center">
      <h1 class="text-xl font-semibold text-gray-800">Custom Page - Fullscreen</h1>
      <div class="flex items-center space-x-4">
        <button (click)="toggleScreenListView()"
          class="flex items-center text-sm text-white bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-md transition-colors">
          <span class="material-icons mr-1 text-lg">{{ showScreenList ? 'dashboard' : 'list' }}</span>
          {{ showScreenList ? 'Dashboard View' : 'List View' }}
        </button>
        <button (click)="exitFullscreen()"
          class="flex items-center text-sm text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md transition-colors">
          <span class="material-icons mr-1 text-lg">fullscreen_exit</span>
          Exit Fullscreen
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="flex flex-1 overflow-hidden">
   
    <!-- Complex Dashboard View -->
    <div  class="flex flex-1 overflow-hidden">
      <!-- Left Sidebar - Tree View -->
      <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto flex flex-col">
        <div class="p-4 flex-1">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-sm font-medium text-gray-500 uppercase tracking-wider">Locations</h2>
            <button *ngIf="selectedArea || selectedScreen" 
                    (click)="clearScreenSelection()"
                    class="text-xs text-blue-600 hover:text-blue-800 font-medium">
              Clear
            </button>
          </div>
          <!-- Show only areas, not locations -->
          <div class="space-y-1" *ngFor="let area of areas">
            <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer" 
                 (click)="onAreaClick(area)"
                 [class.bg-blue-100]="selectedArea?.id === area.id"
                 [class.border-l-4]="selectedArea?.id === area.id"
                 [class.border-blue-500]="selectedArea?.id === area.id">
              <span class="material-icons text-blue-500 text-lg mr-2">business</span>
              <span class="text-sm font-medium">{{ area.name }}</span>
              <span *ngIf="selectedArea?.id === area.id" class="ml-auto text-blue-500">
                <span class="material-icons text-sm">check_circle</span>
              </span>
            </div>
            <div class="space-y-1 pl-6" *ngFor="let screen of area.screens">
              <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer"
                (click)="onScreenClick(screen, area.id)" [class.bg-blue-100]="isScreenSelected(screen.id)">
                <span class="material-icons text-gray-400 text-lg mr-2" [ngClass]="{'text-green-500': getEffectiveScreenStatus(screen) === 'online', 
                               'text-gray-400': getEffectiveScreenStatus(screen) === 'offline', 
                               'text-yellow-500': getEffectiveScreenStatus(screen) === 'maintenance', 
                               'text-red-500': getEffectiveScreenStatus(screen) === 'error'}">
                  tv
                </span>
                <span class="text-sm">{{ screen.name }}</span>
                <span class="ml-auto flex items-center">
                  <span class="h-2.5 w-2.5 rounded-full mr-2"
                    [class]="getStatusColor(getEffectiveScreenStatus(screen))"></span>
                  <span class="text-xs">{{ getEffectiveScreenStatus(screen) }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Screen List View -->
      <app-screen-list *ngIf="showScreenList" class="flex-1" [selectedArea]="selectedArea" [playlists]="playlists" (screenClick)="onScreenListClick($event)"></app-screen-list>

      <!-- Main Content Area -->
      <div *ngIf="!showScreenList" class="flex flex-1 overflow-hidden">
        <!-- Media & Templates Section -->
        <div [class]="isMediaPanelExpanded ? 'w-80' : 'w-12'" class="border-r border-gray-200 bg-white flex flex-col">
          <div class="p-4 border-b border-gray-200" *ngIf="isMediaPanelExpanded">
            <div class="flex items-center justify-between mb-2">
              <h2 class="text-sm font-medium text-gray-700">MEDIA & TEMPLATES</h2>
              <button (click)="toggleMediaPanel()" class="p-1 rounded hover:bg-gray-100">
                <span class="material-icons text-gray-500">chevron_left</span>
              </button>
            </div>
            <div class="space-y-2" *ngIf="mediaTypes$ | async as mediaTypes">
              <div *ngFor="let type of mediaTypes"
                class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer">
                <span class="material-icons text-blue-500 text-lg mr-2">{{ type === 'image' ? 'image' : 'movie'
                  }}</span>
                <span class="text-sm">{{ type | titlecase }}s</span>
              </div>
              <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer">
                <span class="material-icons text-green-500 text-lg mr-2">description</span>
                <span class="text-sm">Templates</span>
              </div>
            </div>
          </div>

          <div class="p-4 border-b border-gray-200" *ngIf="isMediaPanelExpanded">
            <h2 class="text-sm font-medium text-gray-700 mb-2">TRIGGERS</h2>
            <div class="space-y-2">
              <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer">
                <span class="material-icons text-orange-500 text-lg mr-2">schedule</span>
                <span class="text-sm">Scheduled</span>
              </div>
              <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer">
                <span class="material-icons text-red-500 text-lg mr-2">sensors</span>
                <span class="text-sm">Sensors</span>
              </div>
            </div>
          </div>

          <div class="p-4" *ngIf="isMediaPanelExpanded">
            <h2 class="text-sm font-medium text-gray-700 mb-2">SMART FOLDERS</h2>
            <div class="space-y-2">
              <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer">
                <span class="material-icons text-gray-500 text-lg mr-2">folder</span>
                <span class="text-sm">Marketing</span>
              </div>
              <div class="flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer">
                <span class="material-icons text-gray-500 text-lg mr-2">folder</span>
                <span class="text-sm">Promotions</span>
              </div>
            </div>
          </div>

          <div class="p-2 flex justify-center items-center flex-1" *ngIf="!isMediaPanelExpanded"
            (click)="toggleMediaPanel()">
            <button class="p-1 rounded hover:bg-gray-100">
              <span class="material-icons text-gray-500">chevron_right</span>
            </button>
          </div>
        </div>

        <!-- Grid layout for media library, playlist panel, and schedule calendar -->
        <div class="grid grid-cols-1 lg:grid-cols-5 gap-0 w-full">
          <!-- Media Library -->
          <div class="lg:col-span-1">
            <app-media-library 
                [media$]="media$" 
                (mediaDragStart)="onMediaDragStart($event.event, $event.media)"
                (mediaDragEnd)="onMediaDragEnd($event)" 
                (addMediaToPlaylist)="addToPlaylist($event)">
            </app-media-library>
          </div>

          <!-- Playlist Panel -->
          <div class="lg:col-span-1">
            <app-playlist-panel
                [filteredPlaylists]="filteredPlaylists"
                [expandedPlaylists]="getExpandedPlaylistsSet()"
                (playlistDragOver)="onPlaylistDragOver($event.event, $event.playlistId)"
                (playlistDragLeave)="onPlaylistDragLeave($event.event, $event.playlistId)"
                (playlistDrop)="onPlaylistDrop($event.event, $event.playlistId)"
                (toggleAvailable)="toggleAvailablePlaylist($event)"
                (colorPicker)="openColorPicker($event)"
                (dropPlaylistItem)="dropPlaylistItem($event.event, $event.playlist)"
                (playlistItemHover)="onPlaylistItemHover($event.item, $event.event)"
                (playlistItemLeave)="onPlaylistItemLeave()"
                (removeItem)="removeItemFromAvailablePlaylist($event.playlistId, $event.itemIndex)"
                (playlistDragStart)="onPlaylistDragStart($event.event, $event.playlist)"
                (playlistDragEnd)="onPlaylistDragEnd($event)">
            </app-playlist-panel>
          </div>

          <!-- Enhanced Schedule Panel -->
          <div class="lg:col-span-3 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden relative">
            <div class="h-full">
              <app-schedule-calendar [schedules]="schedules"
                [selectedScreenName]="selectedScreen ? getSelectedScreenName() : (selectedArea ? getSelectedAreaName() : null)" 
                (addSchedule)="addScheduleEntry()"
                (deleteSchedule)="removeScheduleFromCalendar($event)"
                (scheduleUpdated)="onScheduleUpdated($event)"
                (playlistDropped)="onPlaylistDropped($event)">
              </app-schedule-calendar>
            </div>
          </div>
        </div>
      </div>

      <!-- Media Preview Dialog -->
      <app-hover-preview-modal *ngIf="mediaPreview.show && mediaPreview.item" [item]="mediaPreview.item"
        (close)="hideMediaPreview()"></app-hover-preview-modal>

      <!-- Hover Preview Modal -->
      <div *ngIf="hoverPreview.show && hoverPreview.item" class="fixed inset-0 z-50 pointer-events-none">
        <div
          class="absolute bg-white rounded-xl shadow-2xl border border-gray-200 p-4 w-full max-w-lg pointer-events-auto"
          [style.left.px]="getModalLeft()" [style.top.px]="getModalTop()" (mouseleave)="onPlaylistItemLeave()">
          <div class="flex justify-between items-start mb-3">
            <h3 class="font-semibold text-gray-900">
              {{ hoverPreview.item.name }}
            </h3>
            <button (click)="hoverPreview.show = false" class="text-gray-400 hover:text-gray-600">
              <span class="material-icons">close</span>
            </button>
          </div>
          <div class="h-64 flex items-center justify-center">
            <!-- Image Preview -->
            <div *ngIf="hoverPreview.item.type === 'image'" class="w-full h-full flex items-center justify-center">
              <img [src]="hoverPreview.item.content?.url" [alt]="hoverPreview.item.name"
                class="max-w-full max-h-full object-contain"
                *ngIf="hoverPreview.item.content?.url; else imageNotAvailable">
            </div>

            <!-- Video Preview -->
            <div *ngIf="hoverPreview.item.type === 'video'" class="w-full h-full flex items-center justify-center">
              <video [src]="hoverPreview.item.content?.url" class="max-w-full max-h-full" controls
                *ngIf="hoverPreview.item.content?.url; else videoNotAvailable">
              </video>
            </div>

            <!-- Webpage Preview -->
            <div *ngIf="hoverPreview.item.type === 'webpage'" class="w-full text-center">
              <p class="text-sm text-gray-600 mb-2">Webpage Preview:</p>
              <a [href]="hoverPreview.item.content?.url" target="_blank" class="text-blue-600 hover:underline break-all"
                *ngIf="hoverPreview.item.content?.url; else webpageNotAvailable">
                {{ hoverPreview.item.content?.url }}
              </a>
            </div>

            <!-- Ticker Preview -->
            <div *ngIf="hoverPreview.item.type === 'ticker'" class="w-full text-center">
              <p class="text-sm text-gray-600 mb-2">Ticker Content:</p>
              <p class="text-gray-800">{{ hoverPreview.item.name }}</p>
            </div>
          </div>
          <div class="mt-3 text-sm text-gray-600">
            <div class="flex justify-between">
              <span class="capitalize">{{ hoverPreview.item.type }}</span>
              <span *ngIf="hoverPreview.item.duration">Duration: {{ hoverPreview.item.duration }}s</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Templates for unavailable content -->
      <ng-template #imageNotAvailable>
        <div class="flex flex-col items-center justify-center text-gray-500">
          <span class="material-icons text-4xl mb-2">image_not_supported</span>
          <p>Image not available</p>
        </div>
      </ng-template>

      <ng-template #videoNotAvailable>
        <div class="flex flex-col items-center justify-center text-gray-500">
          <span class="material-icons text-4xl mb-2">videocam_off</span>
          <p>Video not available</p>
        </div>
      </ng-template>

      <ng-template #webpageNotAvailable>
        <div class="flex flex-col items-center justify-center text-gray-500">
          <span class="material-icons text-4xl mb-2">web_asset_off</span>
          <p>Webpage not available</p>
        </div>
      </ng-template>

      

      <!-- Color Picker Modal -->
      <div *ngIf="colorPickerModal.show"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Choose Playlist Color</h3>
            <button (click)="closeColorPicker()" class="text-gray-400 hover:text-gray-600">
              <span class="material-icons">close</span>
            </button>
          </div>

          <div class="mb-4">
            <p class="text-sm text-gray-600 mb-3">{{ colorPickerModal.playlist?.name }}</p>

            <!-- Predefined Colors -->
            <div class="grid grid-cols-8 gap-2 mb-4">
              <div *ngFor="let color of predefinedColors"
                class="w-8 h-8 rounded-full cursor-pointer border-2 border-gray-200 hover:border-gray-400 transition-colors"
                [style.background-color]="color" [class.ring-2]="colorPickerModal.selectedColor === color"
                [class.ring-blue-500]="colorPickerModal.selectedColor === color" (click)="selectColor(color)">
              </div>
            </div>

            <!-- Custom Color Input -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Custom Color</label>
              <div class="flex items-center space-x-2">
                <input type="color" [(ngModel)]="colorPickerModal.selectedColor"
                  class="w-12 h-8 rounded border border-gray-300 cursor-pointer">
                <input type="text" [(ngModel)]="colorPickerModal.selectedColor" placeholder="#3B82F6"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              </div>
            </div>

            <!-- Preview -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
              <div class="flex items-center p-3 bg-gray-50 rounded-md">
                <div class="w-4 h-4 rounded-full mr-3 border-2 border-white shadow-sm"
                  [style.background-color]="colorPickerModal.selectedColor">
                </div>
                <span class="text-sm text-gray-700">{{ colorPickerModal.playlist?.name }}</span>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button (click)="closeColorPicker()"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
              Cancel
            </button>
            <button (click)="savePlaylistColor()"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
              Save Color
            </button>
          </div>
        </div>
      </div>
    </div> <!-- End Complex Dashboard View -->
  </div> <!-- End Main Content -->
</div>