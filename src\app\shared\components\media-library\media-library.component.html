<div class="media-library-container">
  <div class="p-4">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-sm font-medium text-gray-700">Media Library</h2>
      <div class="flex space-x-2">
        <button class="p-1 rounded hover:bg-gray-100">
          <span class="material-icons text-gray-500 text-lg">grid_view</span>
        </button>
        <button class="p-1 rounded hover:bg-gray-100">
          <span class="material-icons text-gray-500 text-lg">view_list</span>
        </button>
      </div>
    </div>

    <div class="media-items-container" *ngIf="media$ | async as mediaItems">
      <!-- Media items would be dynamically generated here -->
      <div *ngFor="let media of mediaItems" class="relative group media-item-draggable" draggable="true"
        (dragstart)="onMediaDragStart($event, media)" (dragend)="onMediaDragEnd($event)">
        <div class="aspect-video bg-gray-100 rounded-md overflow-hidden relative">
          <img [src]="media.url" [alt]="media.name" class="w-full h-full object-cover">
          <div
            class="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center">
            <span class="material-icons text-xs">drag_indicator</span>
            <span class="ml-1 hidden sm:inline">Drag</span>
          </div>
        </div>
        <div class="mt-1 text-xs text-gray-600 truncate">{{ media.name }}</div>
        <div
          class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
          <button (click)="addToPlaylist(media)" class="p-2 bg-white rounded-full shadow-md mr-2">
            <span class="material-icons text-blue-500">add</span>
          </button>
          <div class="p-2 bg-white rounded-full shadow-md cursor-grab">
            <span class="material-icons text-gray-500">drag_handle</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>