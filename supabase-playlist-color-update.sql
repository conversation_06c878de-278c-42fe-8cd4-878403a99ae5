-- SQL script to add color support to playlists in Supabase
-- Run this script in your Supabase SQL editor

-- Add color column to playlists table
ALTER TABLE playlists 
ADD COLUMN IF NOT EXISTS color VARCHAR(7) DEFAULT '#3B82F6';

-- Add a comment to document the color column
COMMENT ON COLUMN playlists.color IS 'Hex color code for playlist identification (e.g., #3B82F6)';

-- Update existing playlists with default colors if they don't have one
-- This will assign different colors to existing playlists for better identification
WITH numbered_playlists AS (
  SELECT 
    id,
    ROW_NUMBER() OVER (ORDER BY created_at) as row_num
  FROM playlists 
  WHERE color IS NULL OR color = ''
)
UPDATE playlists 
SET color = CASE 
    WHEN numbered_playlists.row_num % 16 = 1 THEN '#3B82F6'  -- Blue
    WHEN numbered_playlists.row_num % 16 = 2 THEN '#10B981'  -- Emerald
    WHEN numbered_playlists.row_num % 16 = 3 THEN '#F59E0B'  -- <PERSON>
    WHEN numbered_playlists.row_num % 16 = 4 THEN '#EF4444'  -- Red
    WHEN numbered_playlists.row_num % 16 = 5 THEN '#8B5CF6'  -- Violet
    WHEN numbered_playlists.row_num % 16 = 6 THEN '#06B6D4'  -- Cyan
    WHEN numbered_playlists.row_num % 16 = 7 THEN '#84CC16'  -- Lime
    WHEN numbered_playlists.row_num % 16 = 8 THEN '#F97316'  -- Orange
    WHEN numbered_playlists.row_num % 16 = 9 THEN '#EC4899'  -- Pink
    WHEN numbered_playlists.row_num % 16 = 10 THEN '#6B7280' -- Gray
    WHEN numbered_playlists.row_num % 16 = 11 THEN '#14B8A6' -- Teal
    WHEN numbered_playlists.row_num % 16 = 12 THEN '#A855F7' -- Purple
    WHEN numbered_playlists.row_num % 16 = 13 THEN '#22C55E' -- Green
    WHEN numbered_playlists.row_num % 16 = 14 THEN '#F43F5E' -- Rose
    WHEN numbered_playlists.row_num % 16 = 15 THEN '#0EA5E9' -- Sky
    ELSE '#D946EF' -- Fuchsia
END
FROM numbered_playlists
WHERE playlists.id = numbered_playlists.id;

-- Create an index on the color column for better performance (optional)
CREATE INDEX IF NOT EXISTS idx_playlists_color ON playlists(color);

-- Verify the changes
SELECT id, name, color, created_at 
FROM playlists 
ORDER BY created_at 
LIMIT 10;