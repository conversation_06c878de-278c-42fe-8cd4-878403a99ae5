 <div class="playlist-panel-container">
  <div class="bg-white">
    <div class="border-b p-2 bg-gray-50 flex justify-between items-center">
      <h3 class="text-xs font-medium text-gray-700">Available Playlists ({{ filteredPlaylists.length }})</h3>
      <button 
        (click)="onCreatePlaylist()"
        class="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded flex items-center"
        title="Create new playlist">
        <span class="material-icons text-xs mr-1">add</span>
        New
      </button>
    </div>

    <div class="divide-y playlist-items-container">
      <div *ngFor="let playlist of filteredPlaylists" class="relative playlist-drop-zone"
        [attr.data-playlist-id]="playlist.id" 
        (dragover)="onPlaylistDragOver($event, playlist.id)"
        (dragleave)="onPlaylistDragLeave($event, playlist.id)" 
        (drop)="onPlaylistDrop($event, playlist.id)"
        draggable="true"
        (dragstart)="onPlaylistDragStart($event, playlist)"
        (dragend)="onPlaylistDragEnd($event)">
        <!-- Drop zone indicator -->
        <div
          class="absolute top-0 left-0 right-0 h-1 bg-blue-500 rounded-t-md opacity-0 transition-opacity z-10 playlist-drop-indicator">
        </div>

        <!-- Playlist Header -->
        <div
          class="p-3 cursor-pointer hover:opacity-90 flex justify-between items-center playlist-header-drop-zone"
          [style.background-color]="playlist.color || '#3B82F6'" [style.color]="'white'"
          (click)="toggleAvailablePlaylist(playlist.id)">
          <div class="flex items-center flex-1 min-w-0">
            <!-- Color Indicator -->
            <div
              class="w-4 h-4 rounded-full mr-3 flex-shrink-0 border-2 border-white shadow-sm bg-white opacity-30"
              [title]="'Playlist color: ' + (playlist.color || '#3B82F6')"
              (click)="$event.stopPropagation(); openColorPicker(playlist)">
            </div>
            <div class="flex-1 min-w-0">
              <div class="font-medium text-white text-sm truncate">
                {{ playlist.name }}
              </div>
              <div class="flex items-center mt-1 text-xs text-white opacity-90">
                <span class="material-icons text-xs mr-1">schedule</span>
                <span>{{ formatDuration(playlist.duration) }}</span>
                <span class="mx-2">•</span>
                <span class="material-icons text-xs mr-1">video_library</span>
                <span>{{ playlist.items.length }} items</span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <!-- Expand/Collapse Indicator -->
            <span class="material-icons text-white">
              {{ isAvailablePlaylistExpanded(playlist.id) ? 'expand_less' : 'expand_more' }}
            </span>
          </div>
        </div>

        <!-- Expanded Media Preview -->
        <div *ngIf="isAvailablePlaylistExpanded(playlist.id)" class="px-3 pb-3 bg-gray-50 border-t">
          <div class="py-2">
            <h4 class="text-xs font-medium text-gray-700 mb-2">Media Items</h4>
            <div class="space-y-2" cdkDropList (cdkDropListDropped)="onDropPlaylistItem($event, playlist)">
              <div *ngFor="let item of playlist.items; let itemIndex = index"
                class="flex items-center p-2 bg-white rounded border playlist-item" cdkDrag>
                <div class="flex-shrink-0 mr-2 text-gray-400 cursor-move drag-handle" cdkDragHandle>
                  <span class="material-icons text-sm">drag_handle</span>
                </div>
                <div
                  class="flex-shrink-0 w-10 h-10 bg-gray-100 rounded flex items-center justify-center overflow-hidden cursor-pointer"
                  (mouseenter)="onPlaylistItemHover(item, $event)" (mouseleave)="onPlaylistItemLeave()">
                  <div *ngIf="item.type === 'image' && item.content?.url">
                    <img [src]="item.content.url" [alt]="item.name" class="w-full h-full object-cover">
                  </div>
                  <div *ngIf="item.type === 'video' && item.content?.thumbnail">
                    <img [src]="item.content.thumbnail" [alt]="item.name" class="w-full h-full object-cover">
                  </div>
                  <div *ngIf="item.type === 'video' && !item.content?.thumbnail">
                    <span class="material-icons text-gray-500">play_circle</span>
                  </div>
                  <div *ngIf="item.type === 'webpage'">
                    <span class="material-icons text-gray-500">language</span>
                  </div>
                  <div *ngIf="item.type === 'ticker'">
                    <span class="material-icons text-gray-500">format_align_left</span>
                  </div>
                  <div
                    *ngIf="(item.type === 'image' && !item.content?.url) || (item.type === 'video' && !item.content?.url && !item.content?.thumbnail)">
                    <span class="material-icons text-gray-500">image_not_supported</span>
                  </div>
                </div>
                <div class="ml-3 min-w-0 flex-1">
                  <div class="text-sm font-medium text-gray-800 truncate">{{ item.name }}</div>
                  <div class="text-xs text-gray-500">{{ item.type | titlecase }}</div>
                </div>
                <button (click)="removeItemFromAvailablePlaylist(playlist.id, itemIndex)"
                  class="ml-2 p-1 rounded hover:bg-red-100 text-red-500">
                  <span class="material-icons text-sm">delete</span>
                </button>
              </div>
              <div *ngIf="playlist.items.length === 0" class="text-center py-4">
                <p class="text-xs text-gray-500">No media in this playlist</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredPlaylists.length === 0" class="empty-state-container">
    <div class="empty-state-icon">
      <span class="material-icons text-xl text-gray-400">playlist_add</span>
    </div>
    <h3 class="text-sm font-medium text-gray-900 mb-2">No Playlists Available</h3>
    <p class="text-xs text-gray-500">
      Create playlists to schedule content for your screens.
    </p>
  </div>
</div>