// src/app/features/api/api-playlist-data.component.ts
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule, JsonPipe } from '@angular/common';
import { PlaylistItem } from '../../models/playlist.model';
import { supabase } from '../../core/services/supabase.config';

@Component({
  selector: 'app-api-playlist-data',
  standalone: true,
  imports: [CommonModule, JsonPipe],
  template: `
    <div *ngIf="loading">Loading playlist data...</div>
    <div *ngIf="error" class="text-red-500">Error: {{ error }}</div>
    <pre *ngIf="!loading && !error">{{ playlistData | json }}</pre>
  `
})
export class ApiPlaylistDataComponent implements OnInit {
  loading = true;
  error: string | null = null;
  playlistData: any = null;

  constructor(
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.loadPlaylistData();
  }

  async loadPlaylistData() {
    try {
      const playlistId = this.route.snapshot.paramMap.get('id');
      
      if (!playlistId) {
        throw new Error('No playlist ID provided');
      }
      
      const { data, error } = await supabase
        .from('playlists')
        .select(`
          *,
          items:playlist_items(*)
        `)
        .eq('id', playlistId)
        .single();
        
      if (error) throw error;
      
      // Map the items to match the same structure as the preview
      const items = (data.items || []).map((item: any) => ({
        id: item.id,
        type: item.type || 'image',
        name: item.name || '',
        duration: item.duration || 10,
        content: {
          url: item.content_url || '',
          thumbnail: item.thumbnail_url || ''
        },
        settings: {
          transition: item.transition || 'fade',
          transitionDuration: item.transition_duration || 0.5,
          scaling: item.scaling || 'fit',
          muted: item.muted || true,
          loop: item.loop || false
        },
        schedule: null
      }));
      
      // Prepare the data in the same format as used by the preview
      this.playlistData = {
        id: data.id,
        name: data.name,
        description: data.description,
        duration: data.duration,
        items: items,
        schedule: data.schedule,
        lastModified: data.last_modified,
        createdBy: data.created_by,
        status: data.status,
        tags: data.tags,
        settings: data.settings
      };
      
      this.loading = false;
      
    } catch (err: any) {
      console.error('Error loading playlist data:', err);
      this.error = err.message || 'Failed to load playlist data';
      this.loading = false;
    }
  }
}