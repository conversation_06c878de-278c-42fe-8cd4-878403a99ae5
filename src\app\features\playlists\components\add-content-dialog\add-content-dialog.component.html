<div class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
  <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4">
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b">
      <h2 class="text-xl font-semibold">Add Content</h2>
      <button
        (click)="onCancel.emit()"
        class="text-gray-400 hover:text-gray-600"
      >
        <span class="material-icons">close</span>
      </button>
    </div>

    <!-- Main Content -->
    <div class="p-6">
      <!-- Tabs -->
      <div class="flex space-x-4 mb-6">
        <button
          (click)="activeTab = 'select'"
          class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          [class.bg-blue-50]="activeTab === 'select'"
          [class.text-blue-600]="activeTab === 'select'"
          [class.bg-gray-50]="activeTab !== 'select'"
          [class.text-gray-600]="activeTab !== 'select'"
        >
          Select Existing
        </button>
        <button
          (click)="activeTab = 'upload'"
          class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          [class.bg-blue-50]="activeTab === 'upload'"
          [class.text-blue-600]="activeTab === 'upload'"
          [class.bg-gray-50]="activeTab !== 'upload'"
          [class.text-gray-600]="activeTab !== 'upload'"
        >
          Upload New
        </button>
      </div>

      <!-- Select Content Tab -->
      @if (activeTab === 'select') {
        <div class="space-y-4">
          <!-- Search and Filter -->
          <div class="flex gap-4 items-center">
            <div class="relative flex-1">
              <input
                type="text"
                [(ngModel)]="searchQuery"
                (ngModelChange)="filterMedia()"
                placeholder="Search media..."
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg"
              />
              <span
                class="material-icons absolute left-3 top-2.5 text-gray-400 text-sm"
              >search</span>
            </div>
            <select
              [(ngModel)]="selectedType"
              (ngModelChange)="filterMedia()"
              class="px-4 py-2 border border-gray-200 rounded-lg bg-white"
            >
              <option value="all">All Types</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
            </select>
          </div>

          <!-- Selection counter -->
          <div class="flex justify-between items-center">
            <p class="text-sm text-gray-600">
              {{ selectedMediaIds.length }} item(s) selected
            </p>
            @if (selectedMediaIds.length > 0) {
              <button 
                (click)="selectedMediaIds = []" 
                class="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear selection
              </button>
            }
          </div>

          <!-- Loading State -->
          @if (loading) {
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          }

          <!-- Error State -->
          @if (error) {
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
              {{ error }}
              <button
                (click)="loadMedia()"
                class="ml-2 text-blue-600 hover:text-blue-700"
              >
                Retry
              </button>
            </div>
          }

          <!-- Media Grid -->
          @if (!loading && !error) {
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-[400px] overflow-y-auto p-1">
              @for (item of filteredMedia; track item.id) {
                <div
                  class="relative rounded-lg overflow-hidden cursor-pointer group border border-gray-100"
                  [class.ring-2]="isSelected(item.id)"
                  [class.ring-blue-500]="isSelected(item.id)"
                  (click)="toggleSelection(item)"
                >
                  <div class="aspect-video relative">
                    @if (item.type === 'image') {
                      <img
                        [src]="item.url"
                        [alt]="item.name"
                        class="w-full h-full object-cover"
                      />
                    } @else {
                      <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                        @if (item.thumbnail_url) {
                          <img
                            [src]="item.thumbnail_url"
                            [alt]="item.name"
                            class="w-full h-full object-cover"
                          />
                        } @else {
                          <span class="material-icons text-4xl text-gray-400">movie</span>
                        }
                        <div class="absolute top-2 right-2 px-2 py-1 bg-black/50 rounded text-white text-xs">
                          {{ formatDuration(item.duration || 0) }}
                        </div>
                      </div>
                    }
                    <div
                      class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                    >
                      <span class="material-icons text-white text-3xl">
                        {{ isSelected(item.id) ? 'check_circle' : 'add_circle' }}
                      </span>
                    </div>
                    
                    <!-- Selection indicator -->
                    @if (isSelected(item.id)) {
                      <div class="absolute top-2 left-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                        <span class="material-icons text-sm">check</span>
                      </div>
                    }
                  </div>
                  <div class="p-3 bg-white border-t">
                    <h3 class="font-medium text-sm truncate">{{ item.name }}</h3>
                    <p class="text-xs text-gray-500">
                      {{ formatFileSize(item.metadata.size) }}
                    </p>
                  </div>
                </div>
              }
            </div>

            @if (filteredMedia.length === 0) {
              <div class="text-center py-8">
                <span class="material-icons text-4xl text-gray-400 mb-2">search_off</span>
                <p class="text-gray-500">No media found</p>
              </div>
            }
          }
        </div>
      }

      <!-- Upload Tab -->
      @if (activeTab === 'upload') {
        <app-media-upload
          (uploadComplete)="onUploadComplete($event)"
          (cancel)="activeTab = 'select'"
        />
      }
    </div>

    <!-- Footer -->
    <div class="flex justify-end gap-3 p-6 border-t">
      <button
        (click)="onCancel.emit()"
        class="px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg"
      >
        Cancel
      </button>
      <button
        (click)="handleSubmit()"
        [disabled]="selectedMediaIds.length === 0"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        Add {{ selectedMediaIds.length }} item(s) to Playlist
      </button>
    </div>
  </div>
</div>