/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Drag handle styles for playlist items */
.drag-handle {
  cursor: grab;
  transition: all 0.2s ease;
  flex-shrink: 0;
  user-select: none;
  
  &:active {
    cursor: grabbing;
  }
  
  &:hover {
    background-color: #f1f5f9;
    border-radius: 0.5rem;
  }
}

/* Playlist item styles */
.playlist-item {
  transition: none; /* Remove transitions that might cause visual artifacts during drag */
  position: relative;
  
  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

/* CDK Drag and Drop Styles */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  background: white;
  border: 1px solid #e5e7eb;
  transform: rotate(0deg);
}

.cdk-drag-placeholder {
  opacity: 0;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.playlist-items.cdk-drop-list-dragging .playlist-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Screen Orientation Styles */
.screen-display {
  transition: transform 0.3s ease-in-out;
  transform-origin: center;
}

.screen-portrait {
  transform: rotate(90deg);
  
  /* Adjust container for rotated content */
  &.screen-container {
    width: 100vh;
    height: 100vw;
    margin: calc((100vw - 100vh) / 2) calc((100vh - 100vw) / 2);
  }
}

.screen-landscape {
  transform: rotate(0deg);
}

/* For TV display rotation - maintains aspect ratio */
.tv-display-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  &.portrait-mode {
    /* Rotate the entire display container */
    transform: rotate(90deg);
    transform-origin: center center;
    width: 100vh;
    height: 100vw;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -50vh;
    margin-top: -50vw;
    
    /* Ensure rotated content fills the screen properly */
    img, video {
      width: 100vh !important;
      height: 100vw !important;
      max-width: none !important;
      max-height: none !important;
      object-fit: cover !important;
    }
  }
}

/* Portrait mode media specific styles */
.portrait-media-item {
  transform: rotate(90deg) !important;
  transform-origin: center center !important;
  width: 100vh !important;
  height: 100vw !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  margin-left: -50vh !important;
  margin-top: -50vw !important;
  object-fit: cover !important;
  z-index: 1 !important;
}

/* Alternative approach - rotate the container and let media adapt */
.tv-display-content.portrait-mode {
  transform: rotate(90deg);
  transform-origin: center center;
  width: 100vh;
  height: 100vw;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -50vh;
  margin-top: -50vw;
  overflow: hidden;
  
  .absolute {
    width: 100%;
    height: 100%;
  }
  
  img, video {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transform: none !important;
    position: relative !important;
    top: auto !important;
    left: auto !important;
    margin: 0 !important;
  }
}
