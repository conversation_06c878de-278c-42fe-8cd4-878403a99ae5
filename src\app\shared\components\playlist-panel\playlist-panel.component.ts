import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Playlist, PlaylistItem as PlaylistItemType } from '../../../models/playlist.model';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-playlist-panel',
  standalone: true,
  imports: [CommonModule, DragDropModule],
  templateUrl: './playlist-panel.component.html',
  styleUrls: ['./playlist-panel.component.scss']
})
export class PlaylistPanelComponent {
  @Input() filteredPlaylists: Playlist[] = [];
  @Input() expandedPlaylists: Set<string> = new Set();

  @Output() playlistDragOver = new EventEmitter<{ event: DragEvent, playlistId: string }>();
  @Output() playlistDragLeave = new EventEmitter<{ event: DragEvent, playlistId: string }>();
  @Output() playlistDrop = new EventEmitter<{ event: DragEvent, playlistId: string }>();
  @Output() toggleAvailable = new EventEmitter<string>();
  @Output() colorPicker = new EventEmitter<Playlist>();
  @Output() dropPlaylistItem = new EventEmitter<{ event: CdkDragDrop<PlaylistItemType[]>, playlist: Playlist }>();
  @Output() playlistItemHover = new EventEmitter<{ item: any, event: MouseEvent }>();
  @Output() playlistItemLeave = new EventEmitter<void>();
  @Output() removeItem = new EventEmitter<{ playlistId: string, itemIndex: number }>();
  
  // New output for dragging playlists to calendar
  @Output() playlistDragStart = new EventEmitter<{ event: DragEvent, playlist: Playlist }>();
  @Output() playlistDragEnd = new EventEmitter<DragEvent>();
  
  // New output for creating playlists
  @Output() createPlaylist = new EventEmitter<void>();

  isAvailablePlaylistExpanded(playlistId: string): boolean {
    return this.expandedPlaylists.has(playlistId);
  }

  formatDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}m ${seconds}s`;
  }

  onPlaylistDragOver(event: DragEvent, playlistId: string) {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }

    this.playlistDragOver.emit({ event, playlistId });
  }

  onPlaylistDragLeave(event: DragEvent, playlistId: string) {
    event.preventDefault();
    event.stopPropagation();
    
    this.playlistDragLeave.emit({ event, playlistId });
  }

  onPlaylistDrop(event: DragEvent, playlistId: string) {
    event.preventDefault();
    event.stopPropagation();
    
    this.playlistDrop.emit({ event, playlistId });
  }

  // New method for handling playlist drag start
  onPlaylistDragStart(event: DragEvent, playlist: Playlist) {
    console.log('Playlist drag started:', playlist);
    
    // Set the drag effect
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copy';
      
      // Store the playlist data as JSON in the drag event
      event.dataTransfer.setData('text/plain', JSON.stringify(playlist));
      
      // Set a custom drag image
      const dragImage = document.createElement('div');
      dragImage.className = 'drag-image';
      dragImage.innerHTML = `
        <div class="flex items-center">
          <span class="material-icons drag-image-icon mr-2">playlist_play</span>
          <span class="truncate max-w-xs">Schedule: ${playlist.name}</span>
        </div>
      `;
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px'; // Move off-screen
      dragImage.style.backgroundColor = playlist.color || '#3B82F6'; // Use playlist color or default blue
      dragImage.style.color = 'white';
      dragImage.style.padding = '8px 12px';
      dragImage.style.borderRadius = '0.5rem';
      dragImage.style.fontSize = '0.875rem';
      dragImage.style.fontWeight = '500';
      dragImage.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
      dragImage.style.pointerEvents = 'none';
      dragImage.style.zIndex = '9999';
      dragImage.style.maxWidth = '200px';
      dragImage.style.display = 'flex';
      dragImage.style.alignItems = 'center';
      dragImage.style.justifyContent = 'center';
      document.body.appendChild(dragImage);
      event.dataTransfer.setDragImage(dragImage, 0, 0);
      
      // Add visual feedback to the dragged element
      const target = event.target as HTMLElement;
      if (target) {
        target.classList.add('playlist-dragging');
      }
      
      // Remove the temporary drag image after a short delay
      setTimeout(() => {
        if (dragImage.parentNode) {
          document.body.removeChild(dragImage);
        }
      }, 0);
    }
    
    this.playlistDragStart.emit({ event, playlist });
  }

  // New method for handling playlist drag end
  onPlaylistDragEnd(event: DragEvent) {
    console.log('Playlist drag ended');
    // Clean up any visual effects if needed
    const target = event.target as HTMLElement;
    if (target) {
      target.classList.remove('playlist-dragging');
    }
    
    this.playlistDragEnd.emit(event);
  }

  toggleAvailablePlaylist(playlistId: string) {
    this.toggleAvailable.emit(playlistId);
  }

  openColorPicker(playlist: Playlist) {
    this.colorPicker.emit(playlist);
  }

  onDropPlaylistItem(event: CdkDragDrop<PlaylistItemType[]>, playlist: Playlist) {
    this.dropPlaylistItem.emit({ event, playlist });
  }

  onPlaylistItemHover(item: any, event: MouseEvent) {
    this.playlistItemHover.emit({ item, event });
  }

  onPlaylistItemLeave() {
    this.playlistItemLeave.emit();
  }

  removeItemFromAvailablePlaylist(playlistId: string, itemIndex: number) {
    this.removeItem.emit({ playlistId, itemIndex });
  }

  onCreatePlaylist() {
    this.createPlaylist.emit();
  }
}
