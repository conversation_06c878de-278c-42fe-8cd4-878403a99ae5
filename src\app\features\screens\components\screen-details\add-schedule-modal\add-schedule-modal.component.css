/* Modal animations */
:host {
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal container animations */
.modal-shadow {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #1f2937;
  outline-offset: 2px;
  border-radius: 8px;
}

/* Remove focus outline for mouse users */
button:focus:not(:focus-visible),
input:focus:not(:focus-visible),
select:focus:not(:focus-visible),
textarea:focus:not(:focus-visible) {
  outline: none;
}

/* Custom scrollbar for modal body */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-w-2xl {
    max-width: 95vw;
  }

  .px-8 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }
}

@media (max-width: 640px) {
  .max-w-2xl {
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .px-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-2xl {
    font-size: 1.25rem;
  }
}

/* Enhanced button styles */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Smooth transitions */
.transition-all {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color, background-color, border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Error state styling */
.bg-red-50 {
  background-color: #fef2f2;
}

.border-red-200 {
  border-color: #fecaca;
}

.text-red-400 {
  color: #f87171;
}

.text-red-800 {
  color: #991b1b;
}

.text-red-700 {
  color: #b91c1c;
}
