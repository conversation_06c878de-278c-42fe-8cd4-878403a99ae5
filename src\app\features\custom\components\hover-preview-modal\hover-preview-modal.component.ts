import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ViewChild, 
  ElementRef, 
  AfterViewInit
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-hover-preview-modal',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50"
      (click)="onBackdropClick($event)"
    >
      <div 
        class="bg-white rounded-lg shadow-xl w-full max-w-2xl"
        (click)="$event.stopPropagation()"
      >
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b">
          <h3 class="text-xl font-semibold">{{ item.name }}</h3>
          <button 
            (click)="close.emit()" 
            class="text-gray-400 hover:text-gray-600"
            title="Close"
          >
            <span class="material-icons">close</span>
          </button>
        </div>

        <!-- Content -->
        <div class="p-6 flex items-center justify-center min-h-[300px]">
          <!-- Image Preview -->
          @if (item.type === 'image') {
            @if (item.content?.url) {
              <img
                [src]="item.content.url"
                [alt]="item.name"
                class="max-w-full max-h-[50vh] object-contain"
              />
            } @else {
              <div class="flex flex-col items-center justify-center text-gray-500">
                <span class="material-icons text-4xl mb-2">image_not_supported</span>
                <p>Image not available</p>
              </div>
            }
          }

          <!-- Video Preview -->
          @if (item.type === 'video') {
            @if (item.content?.url) {
              <video
                #videoPlayer
                [src]="item.content.url"
                class="max-w-full max-h-[50vh]"
                controls
                (loadedmetadata)="onVideoLoaded()"
              ></video>
            } @else {
              <div class="flex flex-col items-center justify-center text-gray-500">
                <span class="material-icons text-4xl mb-2">videocam_off</span>
                <p>Video not available</p>
              </div>
            }
          }

          <!-- Webpage Preview -->
          @if (item.type === 'webpage') {
            @if (item.content?.url) {
              <div class="w-full text-center">
                <p class="text-sm text-gray-600 mb-2">Webpage Preview:</p>
                <a [href]="item.content.url" target="_blank" class="text-blue-600 hover:underline break-all">
                  {{ item.content.url }}
                </a>
              </div>
            } @else {
              <div class="flex flex-col items-center justify-center text-gray-500">
                <span class="material-icons text-4xl mb-2">web_asset_off</span>
                <p>Webpage not available</p>
              </div>
            }
          }

          <!-- Ticker Preview -->
          @if (item.type === 'ticker') {
            <div class="w-full text-center">
              <p class="text-sm text-gray-600 mb-2">Ticker Content:</p>
              <p class="text-gray-800">{{ item.name }}</p>
            </div>
          }
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-between p-4 border-t bg-gray-50">
          <div class="text-sm text-gray-500">
            <span class="capitalize">{{ item.type }}</span>
            @if (item.duration) {
              <span> • {{ item.duration }}s</span>
            }
          </div>
          <button 
            (click)="close.emit()" 
            class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }

    .material-icons {
      font-size: 20px;
      line-height: 1;
      display: block;
    }
  `]
})
export class HoverPreviewModalComponent implements AfterViewInit {
  @Input({ required: true }) item: any;
  @Output() close = new EventEmitter<void>();
  @ViewChild('videoPlayer') videoPlayer?: ElementRef<HTMLVideoElement>;

  constructor(private sanitizer: DomSanitizer) {}

  ngAfterViewInit() {
    if (this.item.type === 'video' && this.videoPlayer && this.item.content?.url) {
      // Try to play the video
      const playPromise = this.videoPlayer.nativeElement.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.log('Autoplay prevented for hover preview:', error);
        });
      }
    }
  }

  onVideoLoaded() {
    // Video metadata loaded
  }

  onBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      this.close.emit();
    }
  }

  getSafeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }
}