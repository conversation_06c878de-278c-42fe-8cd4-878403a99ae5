import { PlaylistScheduleBase } from '../../models/screen.model';
import { Schedule } from '../components/schedule-calendar/schedule-calendar.component';
import { Playlist } from '../../models/playlist.model';

/**
 * Converts PlaylistScheduleBase[] to Schedule[] format for the calendar component
 * @param schedules Array of PlaylistScheduleBase objects
 * @param playlistMap Map of playlist IDs to playlist names
 * @param screenId Optional screen ID to prefix schedule IDs with
 * @returns Array of Schedule objects
 */
export function convertScreenSchedulesToCalendarFormat(
  schedules: PlaylistScheduleBase[],
  playlistMap: { [key: string]: string },
  screenId?: string
): Schedule[] {
  // Create a map to group schedules by their time slots
  const scheduleMap = new Map<string, Schedule>();
  
  schedules.forEach(schedule => {
    // Create a unique key for each schedule based on playlist_id, start_time, and end_time
    // Use a delimiter that won't conflict with UUIDs or time formats
    const key = `${schedule.playlist_id}___${schedule.start_time}___${schedule.end_time}`;
    
    // If we already have this schedule, merge the days of week
    if (scheduleMap.has(key)) {
      const existingSchedule = scheduleMap.get(key)!;
      if (schedule.days_of_week) {
        // Merge days of week, avoiding duplicates
        const existingDays = existingSchedule.daysOfWeek || [];
        const existingDayNames = existingDays.map(day => {
          const dayMap: { [key: number]: string } = {
            0: 'Sunday',
            1: 'Monday',
            2: 'Tuesday',
            3: 'Wednesday',
            4: 'Thursday',
            5: 'Friday',
            6: 'Saturday'
          };
          return dayMap[day] || 'Sunday';
        });
        
        const newDays = schedule.days_of_week || [];
        const allDays = [...existingDayNames, ...newDays];
        
        // Remove duplicates using a more compatible approach
        const uniqueDaysSet = new Set<string>();
        const uniqueDaysArray: string[] = [];
        allDays.forEach(day => {
          if (!uniqueDaysSet.has(day)) {
            uniqueDaysSet.add(day);
            uniqueDaysArray.push(day);
          }
        });
        
        const uniqueDays = uniqueDaysArray.map(day => {
          const dayMap: { [key: string]: number } = {
            'Sunday': 0,
            'Monday': 1,
            'Tuesday': 2,
            'Wednesday': 3,
            'Thursday': 4,
            'Friday': 5,
            'Saturday': 6
          };
          return dayMap[day] !== undefined ? dayMap[day] : 0;
        });
        
        existingSchedule.daysOfWeek = uniqueDays;
      }
    } else {
      // Create a new schedule entry
      scheduleMap.set(key, {
        id: screenId ? `${screenId}___${key}` : key, // Prefix with screen ID if provided
        startTime: schedule.start_time,
        endTime: schedule.end_time,
        playlistName: playlistMap[schedule.playlist_id] || `Unknown Playlist (${schedule.playlist_id})`,
        duration: 0, // Will be calculated or provided by the service
        repeat: 'once',
        status: 'active',
        daysOfWeek: schedule.days_of_week ? schedule.days_of_week.map(day => {
          const dayMap: { [key: string]: number } = {
            'Sunday': 0,
            'Monday': 1,
            'Tuesday': 2,
            'Wednesday': 3,
            'Thursday': 4,
            'Friday': 5,
            'Saturday': 6
          };
          return dayMap[day] !== undefined ? dayMap[day] : 0;
        }) : []
      });
    }
  });
  
  // Convert map values to array
  return Array.from(scheduleMap.values());
}

/**
 * Converts Schedule back to PlaylistScheduleBase format
 * @param schedule Schedule object from calendar component
 * @returns PlaylistScheduleBase object
 */
export function convertCalendarScheduleToScreenFormat(schedule: Schedule): PlaylistScheduleBase {
  return {
    playlist_id: schedule.id.split('___')[0], // Extract playlist ID from the combined ID using the new delimiter
    start_time: schedule.startTime,
    end_time: schedule.endTime,
    priority: 1, // Default priority
    days_of_week: schedule.daysOfWeek ? schedule.daysOfWeek.map(day => {
      const dayMap: { [key: number]: string } = {
        0: 'Sunday',
        1: 'Monday',
        2: 'Tuesday',
        3: 'Wednesday',
        4: 'Thursday',
        5: 'Friday',
        6: 'Saturday'
      };
      return dayMap[day] || 'Sunday';
    }) : []
  };
}