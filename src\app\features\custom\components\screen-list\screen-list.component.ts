import { Component, OnInit, OnChanges, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Area } from '../../../../models/area.model';
import { Screen as ScreenModel } from '../../../../models/screen.model';
import { Playlist } from '../../../../models/playlist.model';
import { TvDisplayComponent } from '../../../../shared/components/tv-display/tv-display.component';

@Component({
  selector: 'app-screen-list',
  standalone: true,
  imports: [CommonModule, TvDisplayComponent],
  templateUrl: './screen-list.component.html',
  styleUrls: ['./screen-list.component.scss']
})
export class ScreenListComponent implements OnInit, OnChanges {
  @Input() selectedArea: Area | null = null;
  @Input() playlists: Playlist[] = [];
  @Output() screenClick: EventEmitter<any> = new EventEmitter();
  
  screens: ScreenModel[] = [];
  filteredScreens: ScreenModel[] = [];
  selectedScreens: Set<string> = new Set();
  searchTerm: string = '';
  
  // Hover state for screen preview
  screenPreview: {
    show: boolean;
    screen: ScreenModel | null;
    mouseX: number;
    mouseY: number;
  } = {
    show: false,
    screen: null,
    mouseX: 0,
    mouseY: 0
  };
  hoverTimeout: any = null;
  
  // Map of playlist ID to playlist object for easy lookup
  playlistMap: { [key: string]: Playlist } = {};

  ngOnInit() {
    this.updateScreensFromArea();
  }
  
  ngOnChanges() {
    this.updateScreensFromArea();
    // Update playlist map when playlists change
    this.playlistMap = {};
    this.playlists.forEach(playlist => {
      this.playlistMap[playlist.id] = playlist;
    });
  }
  
  private updateScreensFromArea() {
    if (this.selectedArea) {
      this.screens = [...this.selectedArea.screens];
    }
    this.filteredScreens = [...this.screens];
  }
  
  onScreenClick(screen: ScreenModel) {
    this.screenClick.emit(screen);
  }
  
  onDragStart(event: DragEvent, screen: ScreenModel) {
    // Implementation for drag start
    event.dataTransfer?.setData('text/plain', screen.id);
  }
  
  onScreenHover(screen: ScreenModel, event: MouseEvent) {
    // Update mouse position
    this.screenPreview.mouseX = event.clientX;
    this.screenPreview.mouseY = event.clientY;
    
    // Clear any existing timeout
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
    
    // Set a timeout to show the preview after a delay
    this.hoverTimeout = setTimeout(() => {
      this.screenPreview = {
        show: true,
        screen: screen,
        mouseX: event.clientX,
        mouseY: event.clientY
      };
    }, 300); // 300ms delay before showing preview
  }
  
  onScreenLeave() {
    // Clear the timeout if the mouse leaves before the preview shows
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }
    
    // Hide the preview immediately when leaving
    this.screenPreview.show = false;
  }
  
  // Get modal position with boundary checking
  getModalLeft(): number {
    // Modal width is 380px
    const modalWidth = 380;
    const windowWidth = window.innerWidth;
    
    // Position to the right of mouse, but ensure it stays within viewport
    let left = this.screenPreview.mouseX + 20;
    if (left + modalWidth > windowWidth) {
      // If it would go off the right edge, position to the left of mouse
      left = this.screenPreview.mouseX - modalWidth - 20;
    }
    
    // Ensure it doesn't go off the left edge either
    return Math.max(10, left);
  }
  
  getModalTop(): number {
    // Modal height is approximately 280px
    const modalHeight = 280;
    const windowHeight = window.innerHeight;
    
    // Position above mouse, but ensure it stays within viewport
    let top = this.screenPreview.mouseY - modalHeight - 20;
    if (top < 10) {
      // If it would go off the top edge, position below mouse
      top = this.screenPreview.mouseY + 20;
    }
    
    // Ensure it doesn't go off the bottom edge either
    return Math.min(windowHeight - modalHeight - 10, top);
  }
  
  getScreenOrientation(screen: ScreenModel): 'portrait' | 'landscape' {
    return screen.orientation || 'landscape';
  }
  
  getScreenStatusClass(screen: ScreenModel): string {
    switch (screen.status) {
      case 'online':
        return 'bg-green-500';
      case 'offline':
        return 'bg-gray-400';
      case 'maintenance':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  }
  
  getScreenStatusText(screen: ScreenModel): string {
    switch (screen.status) {
      case 'online':
        return 'Online';
      case 'offline':
        return 'Offline';
      case 'maintenance':
        return 'Maintenance';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  }
  
  isScreenOffline(screen: ScreenModel): boolean {
    return screen.status === 'offline';
  }
  
  getStatusColor(status: string): string {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'active':
        return 'bg-blue-500';
      case 'critical':
        return 'bg-red-500';
      case 'alert':
        return 'bg-yellow-500';
      case 'inactive':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  }

  getStatusTextColor(status: string): string {
    switch (status) {
      case 'online':
        return 'text-green-700 bg-green-100';
      case 'active':
        return 'text-blue-700 bg-blue-100';
      case 'critical':
        return 'text-red-700 bg-red-100';
      case 'alert':
        return 'text-yellow-700 bg-yellow-100';
      case 'inactive':
        return 'text-gray-700 bg-gray-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  }

  toggleScreenSelection(screenId: string) {
    if (this.selectedScreens.has(screenId)) {
      this.selectedScreens.delete(screenId);
    } else {
      this.selectedScreens.add(screenId);
    }
  }

  isScreenSelected(screenId: string): boolean {
    return this.selectedScreens.has(screenId);
  }

  selectAllScreens() {
    if (this.selectedScreens.size === this.filteredScreens.length) {
      this.selectedScreens.clear();
    } else {
      this.filteredScreens.forEach(screen => this.selectedScreens.add(screen.id));
    }
  }

  onSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value.toLowerCase();
    this.filteredScreens = this.screens.filter(screen =>
      screen.name.toLowerCase().includes(this.searchTerm) ||
      (this.selectedArea?.name.toLowerCase().includes(this.searchTerm) || false) ||
      screen.status.toLowerCase().includes(this.searchTerm)
    );
  }

  publishToSelected() {
    console.log('Publishing to selected screens:', Array.from(this.selectedScreens));
  }

  clearAlerts() {
    console.log('Clearing alerts for screens');
  }

  exportData() {
    console.log('Exporting screen data');
  }
}