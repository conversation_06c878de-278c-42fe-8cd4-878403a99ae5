# Compiled output
/dist
/tmp
/out-tsc
/bazel-out

# Node
/node_modules
npm-debug.log
yarn-error.log
package-lock.json

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings
.env
.env.local
.env.development
.env.test
.env.production

# System Files
.DS_Store
Thumbs.db

# Project specifics
/public
/.firebase
firebase-debug.log
*.log
.angular

# Supabase
.supabase