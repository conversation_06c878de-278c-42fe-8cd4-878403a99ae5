import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ViewChild, 
  ElementRef, 
  AfterViewInit,
  HostListener
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { PlaylistItem } from '../../../../models/playlist.model';

@Component({
  selector: 'app-playlist-hover-modal',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="fixed inset-0 bg-black/70 z-50 flex items-center justify-center transition-opacity duration-200"
      (click)="_handleBackdropClick($event)"
    >
      <!-- Main Content Container -->
      <div 
        class="relative max-w-[90vw] max-h-[90vh] overflow-hidden rounded-lg bg-black"
        [class.cursor-grab]="_isDraggable && !_isDragging"
        [class.cursor-grabbing]="_isDragging"
        (mousedown)="_startDrag($event)"
        (mousemove)="_handleDrag($event)"
        (mouseup)="_stopDrag()"
        (mouseleave)="_stopDrag()"
      >
        <!-- Image Preview -->
        @if (item.type === 'image') {
          @if (item.content.url) {
            <img
              [src]="item.content.url"
              [alt]="item.name"
              class="max-w-full max-h-full object-contain transition-transform duration-200"
              [class.cursor-zoom-in]="!_isZoomed"
              [class.cursor-zoom-out]="_isZoomed"
              [style.transform]="_getTransform()"
              (click)="_handleContentClick($event)"
              (wheel)="_handleWheel($event)"
              (load)="_onMediaLoaded()"
              (error)="_onMediaError()"
              draggable="false"
            />
          } @else {
            <div class="w-full h-full flex flex-col items-center justify-center text-white p-8">
              <span class="material-icons text-6xl mb-4">image_not_supported</span>
              <h3 class="text-2xl font-bold mb-2">Image Not Available</h3>
              <p class="text-gray-300 text-center">The image URL is missing or invalid</p>
            </div>
          }
        }

        <!-- Video Preview -->
        @if (item.type === 'video') {
          @if (item.content.url) {
            <video
              #videoPlayer
              [src]="item.content.url"
              class="max-w-full max-h-full bg-black"
              controls
              autoplay
              playsinline
              (loadedmetadata)="_onMediaLoaded()"
              (error)="_onMediaError()"
            ></video>
          } @else {
            <div class="w-full h-full flex flex-col items-center justify-center text-white p-8">
              <span class="material-icons text-6xl mb-4">videocam_off</span>
              <h3 class="text-2xl font-bold mb-2">Video Not Available</h3>
              <p class="text-gray-300 text-center">The video URL is missing or invalid</p>
            </div>
          }
        }

        <!-- Webpage Preview -->
        @if (item.type === 'webpage') {
          @if (item.content.url) {
            <iframe
              [src]="getSafeUrl(item.content.url)"
              class="w-full h-full min-w-[600px] min-h-[400px]"
              frameborder="0"
              allowfullscreen
            ></iframe>
          } @else {
            <div class="w-full h-full flex flex-col items-center justify-center text-white p-8">
              <span class="material-icons text-6xl mb-4">web_asset_off</span>
              <h3 class="text-2xl font-bold mb-2">Webpage Not Available</h3>
              <p class="text-gray-300 text-center">The webpage URL is missing or invalid</p>
            </div>
          }
        }

        <!-- Ticker Preview -->
        @if (item.type === 'ticker') {
          <div class="w-full h-full flex items-center justify-center bg-gray-900 p-8">
            <div class="text-white text-center max-w-2xl">
              <h3 class="text-2xl font-bold mb-4">{{ item.name }}</h3>
              <p class="text-lg">This is a ticker item that displays scrolling text content.</p>
            </div>
          </div>
        }

        <!-- Toolbar -->
        <div 
          class="absolute top-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 transition-opacity duration-200"
        >
          <!-- Close Button -->
          <button
            (click)="close.emit()"
            class="p-2 text-white/70 hover:text-white rounded-full hover:bg-white/10 transition-colors"
            title="Close (Esc)"
          >
            <span class="material-icons">close</span>
          </button>

          <!-- Divider -->
          <div class="w-px h-6 bg-white/20"></div>

          <!-- Image Controls -->
          @if (item.type === 'image' && item.content.url) {
            <button
              (click)="_zoomOut()"
              class="p-2 text-white/70 hover:text-white rounded-full hover:bg-white/10 transition-colors"
              [disabled]="_scale <= 1"
              title="Zoom Out (-)"
            >
              <span class="material-icons">remove</span>
            </button>

            <span class="text-white/90 min-w-[4rem] text-center text-sm">
              {{ Math.round(_scale * 100) }}%
            </span>

            <button
              (click)="_zoomIn()"
              class="p-2 text-white/70 hover:text-white rounded-full hover:bg-white/10 transition-colors"
              [disabled]="_scale >= _maxZoom"
              title="Zoom In (+)"
            >
              <span class="material-icons">add</span>
            </button>

            <div class="w-px h-6 bg-white/20"></div>

            <button
              (click)="_resetView()"
              class="p-2 text-white/70 hover:text-white rounded-full hover:bg-white/10 transition-colors"
              [disabled]="!_isTransformed"
              title="Reset View (R)"
            >
              <span class="material-icons">restart_alt</span>
            </button>
          }
        </div>

        <!-- Info Bar -->
        <div 
          class="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm p-4"
        >
          <div class="flex items-start justify-between">
            <div>
              <h3 class="text-white font-medium">{{ item.name }}</h3>
              <p class="text-white/70 text-sm mt-1">
                @if (item.type === 'image') {
                  {{ item.duration }}s duration
                } @else {
                  {{ item.type }}
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }

    .material-icons {
      font-size: 20px;
      line-height: 1;
      display: block;
    }
  `]
})
export class PlaylistHoverModalComponent implements AfterViewInit {
  @Input({ required: true }) item!: PlaylistItem;
  @Output() close = new EventEmitter<void>();
  @ViewChild('videoPlayer') videoPlayer?: ElementRef<HTMLVideoElement>;

  // State
  _scale = 1;
  _maxZoom = 4;
  _position = { x: 0, y: 0 };
  _isZoomed = false;
  _isDraggable = false;
  _isDragging = false;
  _dragStart = { x: 0, y: 0 };
  Math = Math;

  constructor(private sanitizer: DomSanitizer) {}

  ngAfterViewInit() {
    if (this.item.type === 'video' && this.videoPlayer && this.item.content?.url) {
      const playPromise = this.videoPlayer.nativeElement.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.log('Autoplay prevented:', error);
        });
      }
    }
  }

  // Dragging functions
  _startDrag(event: MouseEvent) {
    if (this._scale > 1) {
      this._isDragging = true;
      this._dragStart = {
        x: event.clientX - this._position.x,
        y: event.clientY - this._position.y
      };
      event.preventDefault();
    }
  }

  _handleDrag(event: MouseEvent) {
    if (this._isDragging) {
      this._position = {
        x: event.clientX - this._dragStart.x,
        y: event.clientY - this._dragStart.y
      };
      event.preventDefault();
    }
  }

  _stopDrag() {
    this._isDragging = false;
  }

  _handleContentClick(event: MouseEvent) {
    if (!this._isDragging) {
      this._toggleZoom(event);
    }
  }

  _handleWheel(event: WheelEvent) {
    if (!this.item.content?.url) return;
    
    event.preventDefault();
    const target = event.currentTarget as HTMLElement;
    const delta = -Math.sign(event.deltaY) * 0.25;
    const newScale = Math.max(1, Math.min(this._maxZoom, this._scale + delta));
    
    if (newScale !== this._scale) {
      // Calculate cursor position relative to image
      const rect = target.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Calculate new position to zoom towards cursor
      const scaleChange = newScale / this._scale;
      this._position = {
        x: x - (x - this._position.x) * scaleChange,
        y: y - (y - this._position.y) * scaleChange
      };

      this._scale = newScale;
      this._isZoomed = this._scale > 1;
      this._isDraggable = this._isZoomed;
    }
  }

  _handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      this.close.emit();
    }
  }

  // Media handlers
  _onMediaLoaded() {
    // Media loaded successfully
  }

  _onMediaError() {
    console.error('Error loading media for item:', this.item);
  }

  // View controls
  _toggleZoom(event: MouseEvent) {
    event.stopPropagation();
    if (this._isZoomed) {
      this._resetView();
    } else {
      this._scale = 2;
      this._isZoomed = true;
      this._isDraggable = true;
    }
  }

  _zoomIn() {
    this._scale = Math.min(this._scale + 0.5, this._maxZoom);
    this._isZoomed = this._scale > 1;
    this._isDraggable = this._isZoomed;
  }

  _zoomOut() {
    this._scale = Math.max(this._scale - 0.5, 1);
    this._isZoomed = this._scale > 1;
    this._isDraggable = this._isZoomed;
    if (this._scale === 1) {
      this._position = { x: 0, y: 0 };
    }
  }

  _resetView() {
    this._scale = 1;
    this._position = { x: 0, y: 0 };
    this._isZoomed = false;
    this._isDraggable = false;
  }

  // Utility methods
  _getTransform(): string {
    return `translate(${this._position.x}px, ${this._position.y}px) scale(${this._scale})`;
  }

  get _isTransformed(): boolean {
    return this._scale !== 1 || this._position.x !== 0 || this._position.y !== 0;
  }

  getSafeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }
}