# Schedule Playlist Display Fix

## Issue
When all schedules were deleted from a screen, the current playlist was still being displayed in the screen details view even though there were no more schedules.

## Root Cause
The `removeScheduleFromCalendar` methods in both `screen-details.component.ts` and `custom.component.ts` were not properly checking if all schedules had been removed and clearing the `current_playlist` field accordingly.

While the `deleteSchedule` method in `screen-details.component.ts` was correctly handling this case, the `removeScheduleFromCalendar` method (used by the schedule calendar component) was not.

## Solution
Updated both `removeScheduleFromCalendar` methods to check if all schedules have been removed and clear the current playlist when that happens:

1. In `screen-details.component.ts`:
   - Added a check for `updatedUpcoming.length === 0`
   - When true, clear `current_playlist` and `current_playlist_started_at` fields
   - Set `schedule.current` to null and `schedule.upcoming` to empty array

2. In `custom.component.ts`:
   - Applied the same fix as above
   - Removed incorrect references to `this.screens` (which doesn't exist) since this component uses `screens$` Observable

## Verification
- The application now builds successfully
- When all schedules are deleted from a screen, the "Current Playlist" section correctly shows "No playlist currently assigned"
- The live preview correctly shows "No playlist assigned" instead of continuing to display the previous playlist

## Files Modified
1. `src/app/features/screens/components/screen-details/screen-details.component.ts`
2. `src/app/features/custom/custom.component.ts`