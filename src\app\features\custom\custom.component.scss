// Custom styles for the custom component
.custom-page {
  // Add any custom styles here
}

.status-online {
  background-color: #dcfce7;
  color: #166534;
}

.status-offline {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-maintenance {
  background-color: #ffedd5;
  color: #9a3412;
}

.status-error {
  background-color: #fecaca;
  color: #991b1b;
}

.status-unknown {
  background-color: #f3f4f6;
  color: #374151;
}

.material-icons {
  font-size: 1.25rem; /* 20px */
  line-height: 1;
}

/* Playlist specific styles */
.playlist-item {
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* Playlist items with background colors */
.playlist-item-colored {
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.375rem;
  
  &:hover {
    opacity: 0.9;
  }
}

/* Playlist dropdown styles */
.playlist-item-header {
  cursor: pointer;
  user-select: none;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }
}

.playlist-item-preview {
  transition: all 0.3s ease;
  
  &.expanded {
    max-height: 200px;
    opacity: 1;
  }
  
  &.collapsed {
    max-height: 0;
    opacity: 0;
  }
}

/* Available playlist expansion styles */
.available-playlist-header {
  cursor: pointer;
  user-select: none;
}

.available-playlist-media {
  max-height: 200px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

.media-item-thumbnail {
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.playlist-empty {
  .material-icons {
    font-size: 3rem;
    opacity: 0.3;
  }
}

/* Schedule specific styles */
.schedule-item {
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
  
  &.active {
    border-left-color: #10b981;
    background-color: #f0fdf4;
  }
  
  &.inactive {
    border-left-color: #6b7280;
  }
  
  &.completed {
    border-left-color: #3b82f6;
    background-color: #eff6ff;
  }
}

.schedule-time {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.current-time-display {
  .time {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    font-size: 2rem;
    color: #111827;
  }
  
  .date {
    font-size: 0.875rem;
    color: #6b7280;
  }
}

/* Animation for playlist reordering */
.playlist-item.moving {
  opacity: 0.7;
  transform: scale(0.98);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  
  &:hover {
    background: #94a3b8;
  }
}

/* Additional styling for a more polished look */
.hover\:bg-gray-100:hover {
  background-color: #f9fafb;
}

.hover\:text-gray-900:hover {
  color: #111827;
}

.hover\:bg-gray-200:hover {
  background-color: #e5e7eb;
}

.hover\:bg-red-100:hover {
  background-color: #fee2e2;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.text-white {
  color: #ffffff;
}

.rounded-md {
  border-radius: 0.375rem;
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:host(.fullscreen) {
  /* Custom fullscreen component styles */
  display: block;
  height: 100vh;
  width: 100vw;

  /* Scrollbar styling for fullscreen view */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1f2937;
  }

  ::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
}

/* Expandable section styles */
.expandable-header {
  user-select: none;
  
  &:hover {
    background-color: #f9fafb;
  }
}

/* Left sidebar transition */
.left-sidebar {
  transition: width 0.3s ease;
}

/* Media panel transition */
.media-panel {
  transition: width 0.3s ease;
}

/* Playlist header with colored background */
.playlist-header-drop-zone {
  transition: all 0.2s ease;
  border-radius: 0.5rem 0.5rem 0 0;
  
  &:hover {
    opacity: 0.9 !important;
  }
}

/* Drag and drop styles */
.drag-over {
  background-color: #dbeafe !important; /* blue-100 */
  border-color: #3b82f6 !important; /* blue-500 */
  border-width: 2px !important;
  border-style: dashed !important;
  transition: all 0.2s ease;
}

.drag-over-header {
  background-color: #dbeafe !important; /* blue-100 */
  border-color: #3b82f6 !important; /* blue-500 */
  border-width: 2px !important;
  border-style: dashed !important;
  transition: all 0.2s ease;
}

/* Drag image styling */
.drag-image {
  background-color: #3b82f6; /* blue-500 */
  color: white;
  padding: 8px 12px;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  pointer-events: none;
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 200px;
}

.drag-image-icon {
  font-size: 1rem;
}

.media-item-draggable {
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  border: 2px solid transparent;
  
  &:active {
    cursor: grabbing;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6; /* blue-500 */
  }
  
  &.media-item-dragging {
    opacity: 0.6;
    transform: rotate(5deg) scale(0.95);
  }
}

.playlist-drop-zone {
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  position: relative;
  overflow: hidden;
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0.5rem;
    border: 2px dashed transparent;
    pointer-events: none;
    transition: all 0.2s ease;
  }
}

.playlist-drop-zone-active {
  background-color: #dbeafe; /* blue-100 */
  transform: scale(1.01);
  
  &:before {
    border-color: #3b82f6; /* blue-500 */
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .playlist-header-drop-zone {
    position: relative;
    
    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(219, 234, 254, 0.8); /* blue-100 with opacity */
      border-radius: 0.5rem 0.5rem 0 0;
      pointer-events: none;
    }
  }
  
  .playlist-drop-indicator {
    opacity: 1 !important;
  }
  
  /* Add a pulsing effect to indicate it's a drop target */
  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0.5rem;
    border: 2px solid #3b82f6; /* blue-500 */
    animation: pulse 1.5s infinite;
    pointer-events: none;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.playlist-header-drop-zone {
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  position: relative;
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0.5rem;
    border: 2px dashed transparent;
    pointer-events: none;
    transition: all 0.2s ease;
  }
}

.playlist-header-drop-zone-active {
  position: relative;
  
  &:after {
    content: 'Drop here to add to playlist';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    color: #1f2937;
    padding: 4px 8px;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border: 2px dashed rgba(255, 255, 255, 0.5);
    border-radius: 0.5rem 0.5rem 0 0;
    pointer-events: none;
  }
}

.playlist-drop-indicator {
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* CDK Drag and Drop Styles */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  background: white;
  border: 1px solid #e5e7eb;
}

.cdk-drag-placeholder {
  opacity: 0;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.playlist-items-list.cdk-drop-list-dragging .playlist-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Add visual feedback when dragging items within a playlist */
.cdk-drop-list-dragging {
  .playlist-item {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
  
  /* Add a subtle highlight to the drop list */
  background-color: #f8fafc;
  border-radius: 0.5rem;
}

.playlist-item {
  cursor: move; /* fallback if grab cursor is not supported */
  cursor: grab;
  transition: all 0.2s ease;
  
  &:active {
    cursor: grabbing;
  }
  
  &:hover {
    background-color: #f9fafb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .drag-handle {
      background-color: #f1f5f9;
      border-radius: 0.25rem;
      padding: 0.125rem;
    }
  }
}

.drag-handle {
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #e2e8f0;
    border-radius: 0.25rem;
    padding: 0.125rem;
  }
}

/* Enhanced Schedule Panel Styles */
.schedule-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  
  .status-cards {
    .status-card {
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
    }
    
    .time-display {
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-variant-numeric: tabular-nums;
      letter-spacing: -0.025em;
    }
    
    .pulse-dot {
      animation: pulse-dot 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    .stats-grid {
      .stat-item {
        transition: all 0.2s ease;
        
        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

/* Enhanced Calendar Styles */
.schedule-calendar {
  .calendar-header {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    
    .day-selector {
      .day-button {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        
        &:hover:not(.active) {
          transform: translateY(-1px);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        &.active {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
        }
      }
    }
    
    .view-toggle {
      .toggle-button {
        transition: all 0.2s ease;
        
        &.active {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }
  
  .calendar-grid {
    .time-slot {
      transition: all 0.2s ease;
      
      &:hover {
        background-color: rgba(59, 130, 246, 0.05);
        
        .time-label {
          background-color: #3b82f6;
          color: white;
          transform: scale(1.05);
        }
        
        .time-line {
          background-color: #3b82f6;
          height: 2px;
        }
      }
    }
    
    .current-time-indicator {
      .time-line {
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
      }
      
      .time-dot {
        box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
      }
      
      .time-badge {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
      }
    }
    
    .schedule-event {
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        
        .event-actions {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      .event-actions {
        opacity: 0;
        transform: translateX(10px);
        transition: all 0.2s ease;
        
        .action-button {
          transition: all 0.2s ease;
          
          &:hover {
            transform: scale(1.1);
          }
          
          &.edit-button:hover {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
          }
          
          &.delete-button:hover {
            background-color: rgba(239, 68, 68, 0.1);
            color: #ef4444;
          }
        }
      }
      
      .status-badge {
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        
        .status-dot {
          animation: pulse-status 2s ease-in-out infinite;
        }
      }
    }
  }
  
  .empty-state {
    .empty-icon {
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.1) rotate(5deg);
      }
    }
    
    .add-button {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);
      }
    }
  }
}

@keyframes pulse-status {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* Floating Action Buttons */
.floating-actions {
  .fab {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      transform: translateY(-2px) scale(1.05);
    }
    
    &.primary {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
      
      &:hover {
        box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.5);
      }
    }
    
    &.secondary {
      background: white;
      border: 1px solid rgba(0, 0, 0, 0.1);
      
      &:hover {
        box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
      }
    }
    
    .fab-icon {
      transition: transform 0.2s ease;
    }
    
    &:hover .fab-icon {
      transform: scale(1.1);
    }
  }
}

/* Tooltip Enhancements */
.schedule-tooltip {
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: tooltip-appear 0.2s ease-out;
}

@keyframes tooltip-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .schedule-panel {
    .status-cards {
      .status-card {
        min-width: auto;
        width: 100%;
      }
    }
  }
  
  .schedule-calendar {
    .calendar-header {
      .day-selector {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        
        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
  }
  
  .floating-actions {
    bottom: 1rem;
    right: 1rem;
    
    .fab {
      width: 48px;
      height: 48px;
    }
  }
}

/* Color Picker Styles */
.color-picker-modal {
  .color-grid {
    .color-option {
      transition: all 0.2s ease;
      position: relative;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
      
      &.selected {
        transform: scale(1.1);
        
        &::after {
          content: '✓';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-weight: bold;
          font-size: 0.75rem;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
      }
    }
  }
  
  .custom-color-input {
    input[type="color"] {
      -webkit-appearance: none;
      border: none;
      cursor: pointer;
      
      &::-webkit-color-swatch-wrapper {
        padding: 0;
      }
      
      &::-webkit-color-swatch {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
      }
    }
  }
  
  .color-preview {
    .preview-item {
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* Playlist Color Indicator Styles */
.playlist-color-indicator {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  &:active {
    transform: scale(1.1);
  }
}

/* Enhanced playlist header with color */
.playlist-header-with-color {
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
  
  &:hover {
    border-left-width: 6px;
    padding-left: 14px; /* Adjust for wider border */
  }
}

/* Color-coded playlist items */
.playlist-item-with-color {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    border-radius: 0 2px 2px 0;
    transition: width 0.2s ease;
  }
  
  &:hover::before {
    width: 5px;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .schedule-panel {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    
    .status-cards {
      .status-card {
        background: rgba(31, 41, 55, 0.95);
        border: 1px solid rgba(75, 85, 99, 0.3);
        color: #f9fafb;
      }
    }
  }
  
  .schedule-calendar {
    .calendar-header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }
    
    .calendar-grid {
      background: #1f2937;
      
      .time-slot:hover {
        background-color: rgba(59, 130, 246, 0.1);
      }
      
      .schedule-event {
        background: rgba(31, 41, 55, 0.9);
        border: 1px solid rgba(75, 85, 99, 0.3);
        color: #f9fafb;
      }
    }
  }
  
  .color-picker-modal {
    background: #1f2937;
    color: #f9fafb;
    
    .color-grid .color-option {
      border-color: #374151;
    }
  }
}