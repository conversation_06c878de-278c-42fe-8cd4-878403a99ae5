import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule, SlicePipe } from '@angular/common';
import { Screen } from '../../../../models/screen.model';
import { PlaylistService } from '../../../playlists/services/playlist.service';
import { Playlist } from '../../../../models/playlist.model';

@Component({
  selector: 'app-screen-table',
  standalone: true,
  imports: [CommonModule, SlicePipe],
  template: `
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Name
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Channel
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Resolution
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Current Playlist
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Last Ping
            </th>
            <th
              class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          @for (screen of screens; track screen.id) {
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="flex items-center">
                <span
                  class="h-2.5 w-2.5 rounded-full mr-2"
                  [class]="getStatusColor(screen.status)"
                ></span>
                {{ screen.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ screen.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ screen.channel_name }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ screen.resolution }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              @if (screen.current_playlist && playlists[screen.id]) {
                <div class="flex items-center">
                  <div class="flex space-x-1">
                    @for (item of playlists[screen.id].items | slice:0:5; track item.id) {
                      <div class="w-8 h-8 rounded border overflow-hidden">
                        @switch (item.type) {
                          @case ('image') {
                            <img [src]="item.content.thumbnail || item.content.url" 
                                 [alt]="item.name" 
                                 class="w-full h-full object-cover">
                          }
                          @case ('video') {
                            <div class="w-full h-full bg-blue-100 flex items-center justify-center">
                              <span class="material-icons text-blue-500 text-xs">play_arrow</span>
                            </div>
                          }
                          @case ('webpage') {
                            <div class="w-full h-full bg-green-100 flex items-center justify-center">
                              <span class="material-icons text-green-500 text-xs">language</span>
                            </div>
                          }
                          @case ('ticker') {
                            <div class="w-full h-full bg-purple-100 flex items-center justify-center">
                              <span class="material-icons text-purple-500 text-xs">text_scroll</span>
                            </div>
                          }
                        }
                      </div>
                    }
                    @if (playlists[screen.id].items.length > 5) {
                      <div class="w-8 h-8 rounded border bg-gray-100 flex items-center justify-center">
                        <span class="text-xs text-gray-500">+{{ playlists[screen.id].items.length - 5 }}</span>
                      </div>
                    }
                  </div>
                </div>
              } @else if (screen.current_playlist) {
                <span class="text-blue-600">Loading...</span>
              } @else {
                <span class="text-gray-500">No playlist</span>
              }
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(screen.last_ping) }}</td>
            <td
              class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
            >
              <button
                (click)="edit.emit(screen)"
                class="text-indigo-600 hover:text-indigo-900 mr-4"
              >
                Edit
              </button>
              <button
                (click)="delete.emit(screen)"
                class="text-red-600 hover:text-red-900"
              >
                Delete
              </button>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  `,
})
export class ScreenTableComponent implements OnInit, OnChanges {
  @Input() screens: Screen[] = [];
  @Output() edit = new EventEmitter<Screen>();
  @Output() delete = new EventEmitter<Screen>();

  playlists: { [key: string]: Playlist } = {};

  constructor(private playlistService: PlaylistService) {}

  ngOnInit() {
    this.loadPlaylists();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['screens'] && !changes['screens'].firstChange) {
      this.loadPlaylists();
    }
  }

  loadPlaylists() {
    // Clear existing playlists
    this.playlists = {};
    
    // Load playlist details for all screens with current playlists (regardless of status)
    this.screens
      .filter(screen => screen.current_playlist)
      .forEach(screen => {
        if (screen.current_playlist) {
          this.playlistService.getPlaylist(screen.current_playlist).subscribe({
            next: (playlist) => {
              this.playlists[screen.id] = playlist;
            },
            error: (error) => {
              console.error('Error loading playlist for screen:', screen.id, error);
              // Remove the playlist reference if there's an error
              // This will show "No playlist" instead of "Loading..."
              screen.current_playlist = null;
            }
          });
        }
      });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'online':
        return 'bg-green-400';
      case 'offline':
        return 'bg-gray-400';
      case 'maintenance':
        return 'bg-yellow-400';
      case 'error':
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'Never';
    
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffSec = Math.floor(diffMs / 1000);
      const diffMin = Math.floor(diffSec / 60);
      const diffHour = Math.floor(diffMin / 60);
      const diffDay = Math.floor(diffHour / 24);
      
      if (diffSec < 60) return `${diffSec}s ago`;
      if (diffMin < 60) return `${diffMin}m ago`;
      if (diffHour < 24) return `${diffHour}h ago`;
      if (diffDay < 7) return `${diffDay}d ago`;
      
      return date.toLocaleDateString();
    } catch (e) {
      return 'Invalid date';
    }
  }
}
