/* Media Library Container */
.media-library-container {
  width: 100%;
  background-color: #ffffff; /* bg-white equivalent */
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0;
  box-shadow: none;
}

/* Media items container with proper scrolling */
.media-items-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 120px); /* Adjust based on header height */
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem; /* gap-4 equivalent */
  padding-right: 0.5rem; /* Add some padding for scrollbar */
}

/* Scrollbar styling */
.media-items-container::-webkit-scrollbar {
  width: 6px;
}

.media-items-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.media-items-container::-webkit-scrollbar-thumb {
  background: #c7c7c7;
  border-radius: 10px;
}

.media-items-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Media item styling */
.media-item-draggable {
  transition: all 0.2s ease;
  cursor: grab;
}

.media-item-draggable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.media-item-draggable:active {
  cursor: grabbing;
}

/* Visual feedback when dragging */
.media-item-dragging {
  opacity: 0.5;
  transform: scale(0.98);
}

/* Drag image styling */
.drag-image {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}

.drag-image-icon {
  font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .media-library-container {
    width: 100%;
  }
  
  .media-items-container {
    max-height: 400px;
  }
}