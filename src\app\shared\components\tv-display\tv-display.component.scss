.tv-display {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  
  &.portrait {
    .tv-frame {
      transform: rotate(90deg);
      transform-origin: center;
      width: 100%;
      height: 350px;
      margin: 30px auto;
    }
  }
  
  &.landscape {
    .tv-frame {
      width: 100%;
      height: 350px;
      margin: 0 auto;
    }
  }
  
  // Compact version for screen list
  &.compact {
    max-width: 350px;
    
    &.portrait, &.landscape {
      .tv-frame {
        width: 100%;
        height: 100%;
        margin: 0;
      }
    }
    
    .playlist-info {
      padding: 8px;
      font-size: 12px;
    }
    
    .playlist-name {
      font-size: 14px;
      font-weight: 800;
    }
    
    .item-info {
      font-size: 11px;
      font-weight: 600;
    }
    
    .no-content {
      p {
        font-size: 14px;
        font-weight: 600;
      }
    }
    
    .offline-content {
      p {
        font-size: 14px;
        font-weight: 600;
      }
    }
    
    .placeholder-content {
      .material-icons {
        font-size: 40px;
      }
      
      p {
        font-size: 12px;
        font-weight: 600;
      }
    }
    
    // Special styles for hover preview
    &.hover-preview {
      .tv-frame {
        box-shadow: none;
        padding: 4px;
      }
      
      .tv-stand {
        display: none;
      }
      
      .playlist-info {
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.5), transparent);
        padding: 10px;
        font-size: 13px;
      }
      
      .playlist-name {
        font-size: 16px;
        font-weight: 800;
        letter-spacing: 0.8px;
      }
      
      .item-info {
        font-size: 12px;
        font-weight: 600;
      }
      
      .no-content,
      .offline-content {
        .material-icons {
          font-size: 48px;
        }
        
        p {
          font-size: 16px;
          font-weight: 600;
        }
      }
      
      .placeholder-content {
        .material-icons {
          font-size: 44px;
        }
        
        p {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
  
  // Offline state
  &.offline {
    .tv-screen {
      background: #1a1a1a;
    }
  }
}

.tv-frame {
  position: relative;
  background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
  border-radius: 2px;
  padding: 2px;
  box-shadow: 
    0 1px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.tv-screen {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 1px;
  overflow: hidden;
  position: relative;
}

.playlist-display {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
  position: relative;
}

.media-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.media-content {
  width: 100%;
  height: 100%;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
}

.media-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  // For hover preview, make sure content is centered and visible
  .compact.hover-preview & {
    align-items: center;
    justify-content: center;
  }
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: rgba(0, 0, 0, 0.5);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  transition: width 0.1s linear;
  box-shadow: 0 0 3px rgba(59, 130, 246, 0.8);
}

// For compact hover preview, make it even more prominent
.compact.hover-preview {
  .progress-bar {
    height: 6px;
  }
  
  .progress-fill {
    box-shadow: 0 0 4px rgba(59, 130, 246, 0.9);
  }
}

.playlist-info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 8px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.4), transparent);
  color: white;
  font-size: 11px;
  
  .playlist-name {
    font-weight: 700;
    margin-bottom: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    letter-spacing: 0.5px;
  }
  
  .item-info {
    opacity: 0.9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
  }
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  
  .material-icons {
    font-size: 40px;
    margin-bottom: 10px;
    opacity: 0.9;
  }
  
  p {
    margin: 0;
    font-size: 12px;
    opacity: 0.95;
    font-weight: 500;
  }
  
  // For compact hover preview
  .compact.hover-preview & {
    .material-icons {
      font-size: 48px;
    }
    
    p {
      font-size: 14px;
    }
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  
  .spinner {
    width: 30px;
    height: 30px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.95;
    font-weight: 600;
  }
  
  // For compact hover preview
  .compact.hover-preview & {
    .spinner {
      width: 40px;
      height: 40px;
      border-width: 5px;
    }
    
    p {
      font-size: 16px;
    }
  }
}

.no-content,
.offline-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  
  .material-icons {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.6;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
  }
  
  .playlist-preview {
    text-align: center;
    margin-top: 8px;
    
    p {
      font-size: 12px;
    }
  }
  
  // For compact hover preview
  .compact.hover-preview & {
    .material-icons {
      font-size: 56px;
    }
    
    p {
      font-size: 16px;
    }
  }
}

.offline-content {
  .material-icons {
    color: #9ca3af;
  }
  
  p:first-of-type {
    color: #6b7280;
    font-weight: 600;
    font-size: 16px;
  }
  
  p:last-of-type {
    color: #9ca3af;
    font-size: 12px;
  }
  
  // For compact hover preview
  .compact.hover-preview & {
    p:first-of-type {
      font-size: 18px;
    }
    
    p:last-of-type {
      font-size: 14px;
    }
  }
}

.tv-stand {
  width: 30px;
  height: 3px;
  background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
  border-radius: 1px;
  margin: 2px auto 0;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 45px;
    height: 1px;
    background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
    border-radius: 1px;
  }
}

.ticker-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  overflow: hidden;
  
  .ticker-text {
    color: white;
    font-size: 16px;
    font-weight: 700;
    white-space: nowrap;
    animation: ticker 10s linear infinite;
    padding-left: 100%;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
  
  // For compact hover preview
  .compact.hover-preview & {
    .ticker-text {
      font-size: 18px;
    }
  }
}

@keyframes ticker {
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}