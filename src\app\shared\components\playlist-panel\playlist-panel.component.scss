/* Ensure the playlist panel scrolls properly */
:host {
  display: block;
  height: 100%;
}

.playlist-panel-container {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  box-shadow: none;
}

.playlist-items-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

/* Improve drag and drop visual feedback */
.playlist-drop-zone-active {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
  border-radius: 0.375rem;
}

.playlist-header-drop-zone-active {
  background-color: rgba(59, 130, 246, 0.8) !important;
}

.playlist-drop-indicator {
  opacity: 1 !important;
}

/* Improve playlist item styling */
.playlist-item {
  transition: all 0.2s ease;
}

.playlist-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.playlist-item.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid #e5e7eb;
}

.playlist-item.cdk-drag-placeholder {
  opacity: 0;
}

.playlist-item.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Add visual feedback for drag handle */
.drag-handle:hover {
  color: #1f2937;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

/* Visual feedback for playlist dragging */
.playlist-dragging {
  opacity: 0.7;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

/* Empty state improvements */
.empty-state-container {
  padding: 1.5rem;
  text-align: center;
}

.empty-state-icon {
  width: 3rem;
  height: 3rem;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
}

.empty-state-icon .material-icons {
  font-size: 1.5rem;
  color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .playlist-panel-container {
    max-height: 400px;
  }
}