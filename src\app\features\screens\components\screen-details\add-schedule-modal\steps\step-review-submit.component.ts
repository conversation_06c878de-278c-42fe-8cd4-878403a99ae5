import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ScheduleFormData } from '../models/schedule-form.model';
import { Playlist } from '../../../../../../models/playlist.model';

interface PriorityOption {
  value: number;
  label: string;
  description: string;
}

@Component({
  selector: 'app-step-review-submit',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="space-y-6">
      <!-- Step Header -->
      <div class="text-center">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Review Your Schedule</h3>
        <p class="text-gray-600">Please review the schedule details before submitting</p>
      </div>

      <!-- Schedule Summary Card -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h4 class="text-lg font-semibold text-blue-900 mb-2">
              {{ getSelectedPlaylistName() }}
            </h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center text-blue-700">
                <span class="material-icons text-sm mr-2">schedule</span>
                {{ formatTime(formData.start_time) }} - {{ formatTime(formData.end_time) }}
                <span class="ml-2 text-blue-600">({{ calculateDuration() }})</span>
              </div>
              <div class="flex items-center text-blue-700">
                <span class="material-icons text-sm mr-2">calendar_today</span>
                {{ formatDays() }}
              </div>
              <div class="flex items-center text-blue-700">
                <span class="material-icons text-sm mr-2">flag</span>
                {{ getPriorityLabel() }}
              </div>
            </div>
          </div>
          <div class="ml-4">
            <div 
              class="px-3 py-1 rounded-full text-xs font-medium"
              [class]="getPriorityBadgeClass()"
            >
              Priority {{ formData.priority }}
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Review Sections -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        <!-- Playlist Details -->
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h5 class="font-medium text-gray-900 mb-3 flex items-center">
            <span class="material-icons text-blue-600 mr-2 text-sm">playlist_play</span>
            Playlist Details
          </h5>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Name:</span>
              <span class="font-medium text-gray-900">{{ getSelectedPlaylistName() }}</span>
            </div>
            @if (getSelectedPlaylist()?.description) {
              <div class="flex justify-between">
                <span class="text-gray-600">Description:</span>
                <span class="text-gray-900 text-right max-w-32 truncate">{{ getSelectedPlaylist()?.description }}</span>
              </div>
            }
            <div class="flex justify-between">
              <span class="text-gray-600">Items:</span>
              <span class="text-gray-900">{{ getSelectedPlaylist()?.items?.length || 0 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Duration:</span>
              <span class="text-gray-900">{{ formatPlaylistDuration() }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Status:</span>
              <span 
                class="px-2 py-1 text-xs font-medium rounded-full"
                [class]="getPlaylistStatusClass()"
              >
                {{ getSelectedPlaylist()?.status || 'Unknown' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Schedule Details -->
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h5 class="font-medium text-gray-900 mb-3 flex items-center">
            <span class="material-icons text-blue-600 mr-2 text-sm">event</span>
            Schedule Details
          </h5>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Start Time:</span>
              <span class="font-medium text-gray-900">{{ formatTime(formData.start_time) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">End Time:</span>
              <span class="font-medium text-gray-900">{{ formatTime(formData.end_time) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Duration:</span>
              <span class="text-gray-900">{{ calculateDuration() }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Priority:</span>
              <span class="text-gray-900">{{ getPriorityLabel() }}</span>
            </div>
            <div class="flex justify-between items-start">
              <span class="text-gray-600">Days:</span>
              <div class="text-right">
                <div class="flex flex-wrap gap-1 justify-end">
                  @for (day of formData.days_of_week; track day) {
                    <span class="px-1.5 py-0.5 bg-blue-100 text-blue-800 text-xs rounded">
                      {{ getShortDayName(day) }}
                    </span>
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Weekly Schedule Preview -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h5 class="font-medium text-gray-900 mb-3 flex items-center">
          <span class="material-icons text-blue-600 mr-2 text-sm">view_week</span>
          Weekly Schedule Preview
        </h5>
        <div class="grid grid-cols-7 gap-2">
          @for (day of allDays; track day) {
            <div class="text-center">
              <div class="text-xs font-medium text-gray-600 mb-2">
                {{ getShortDayName(day) }}
              </div>
              <div 
                class="w-full h-16 rounded border-2 flex flex-col items-center justify-center text-xs transition-colors"
                [class]="isDaySelected(day) 
                  ? 'border-blue-600 bg-blue-600 text-white' 
                  : 'border-gray-200 bg-gray-50 text-gray-400'"
              >
                @if (isDaySelected(day)) {
                  <span class="material-icons text-sm mb-1">play_arrow</span>
                  <div class="text-xs leading-tight">
                    {{ formatTimeShort(formData.start_time) }}<br>
                    {{ formatTimeShort(formData.end_time) }}
                  </div>
                } @else {
                  <span class="material-icons text-lg">remove</span>
                }
              </div>
            </div>
          }
        </div>
      </div>

      <!-- Important Notes -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start">
          <span class="material-icons text-yellow-600 mr-3 mt-0.5">info</span>
          <div>
            <h5 class="font-medium text-yellow-800 mb-2">Important Notes</h5>
            <ul class="text-sm text-yellow-700 space-y-1">
              <li>• This schedule will {{ formData.priority === 1 ? 'override' : 'work alongside' }} existing schedules based on priority</li>
              <li>• The playlist will automatically start and stop at the specified times</li>
              <li>• You can edit or delete this schedule after creation</li>
              @if (formData.priority === 1) {
                <li>• <strong>High priority:</strong> This schedule will take precedence over others during overlapping times</li>
              }
            </ul>
          </div>
        </div>
      </div>

      <!-- Submission Status -->
      @if (submitting) {
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
            <span class="text-blue-700 font-medium">Creating your schedule...</span>
          </div>
        </div>
      }
    </div>
  `
})
export class StepReviewSubmitComponent {
  @Input() formData!: ScheduleFormData;
  @Input() availablePlaylists: Playlist[] = [];
  @Input() priorityOptions: PriorityOption[] = [];
  @Input() submitting = false;

  allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  getSelectedPlaylist(): Playlist | undefined {
    return this.availablePlaylists.find(p => p.id === this.formData.playlist_id);
  }

  getSelectedPlaylistName(): string {
    return this.getSelectedPlaylist()?.name || 'Unknown Playlist';
  }

  formatTime(time: string): string {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  formatTimeShort(time: string): string {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes}${ampm}`;
  }

  calculateDuration(): string {
    if (!this.formData.start_time || !this.formData.end_time) return '';

    const start = new Date(`1970-01-01T${this.formData.start_time}:00`);
    const end = new Date(`1970-01-01T${this.formData.end_time}:00`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    const diffMinutes = (diffMs % (1000 * 60 * 60)) / (1000 * 60);

    if (diffHours >= 1) {
      return diffMinutes > 0 
        ? `${Math.floor(diffHours)}h ${Math.floor(diffMinutes)}m`
        : `${Math.floor(diffHours)}h`;
    } else {
      return `${Math.floor(diffMinutes)}m`;
    }
  }

  formatDays(): string {
    if (this.formData.days_of_week.length === 0) return 'No days selected';
    if (this.formData.days_of_week.length === 7) return 'Every day';
    
    const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const weekends = ['Saturday', 'Sunday'];
    
    const selectedWeekdays = this.formData.days_of_week.filter(day => weekdays.includes(day));
    const selectedWeekends = this.formData.days_of_week.filter(day => weekends.includes(day));
    
    if (selectedWeekdays.length === 5 && selectedWeekends.length === 0) {
      return 'Weekdays (Mon-Fri)';
    }
    if (selectedWeekdays.length === 0 && selectedWeekends.length === 2) {
      return 'Weekends (Sat-Sun)';
    }
    
    return this.formData.days_of_week.map(day => this.getShortDayName(day)).join(', ');
  }

  getPriorityLabel(): string {
    const option = this.priorityOptions.find(p => p.value === this.formData.priority);
    return option ? option.label : `Priority ${this.formData.priority}`;
  }

  getPriorityBadgeClass(): string {
    switch (this.formData.priority) {
      case 1:
        return 'bg-red-100 text-red-800';
      case 2:
        return 'bg-yellow-100 text-yellow-800';
      case 3:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPlaylistStatusClass(): string {
    const status = this.getSelectedPlaylist()?.status;
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatPlaylistDuration(): string {
    const playlist = this.getSelectedPlaylist();
    if (!playlist?.duration) return 'Unknown';
    
    const seconds = playlist.duration;
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  }

  getShortDayName(day: string): string {
    return day.substring(0, 3);
  }

  isDaySelected(day: string): boolean {
    return this.formData.days_of_week.includes(day);
  }
}
