// playlist-card.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { Playlist } from '../../../../models/playlist.model';
import { PlaylistService } from '../../../playlists/services/playlist.service';

// Add interface for color picker modal
interface ColorPickerModal {
  show: boolean;
  selectedColor: string;
}

@Component({
  selector: 'app-playlist-card',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './playlist-card.component.html',
})

export class PlaylistCardComponent implements OnInit, OnDestroy {
  @Input({ required: true }) playlist!: Playlist;
  @Output() preview = new EventEmitter<Playlist>();
  @Output() edit = new EventEmitter<Playlist>();
  @Output() delete = new EventEmitter<Playlist>();
  @Output() colorChanged = new EventEmitter<Playlist>();

  currentIndex = 0;
  private slideshowInterval: any;
  private readonly SLIDE_DURATION = 3000; // 3 seconds

  // Color picker properties
  colorPickerModal: ColorPickerModal = {
    show: false,
    selectedColor: '#3B82F6'
  };
  
  // Predefined colors for playlists
  predefinedColors = [
    '#3B82F6', // Blue
    '#10B981', // Emerald
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#8B5CF6', // Violet
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange
    '#EC4899', // Pink
    '#6B7280', // Gray
    '#14B8A6', // Teal
    '#A855F7', // Purple
    '#22C55E', // Green
    '#F43F5E', // Rose
    '#0EA5E9', // Sky
    '#D946EF'  // Fuchsia
  ];

  ngOnInit() {
    this.currentIndex = 0;
  }

  constructor(private playlistService: PlaylistService) {}

  ngOnDestroy() {
    this.stopSlideshow();
  }

  startSlideshow() {
    if (this.playlist.items.length <= 1) return;
    
    // Clear any existing interval first
    this.stopSlideshow();
    
    this.slideshowInterval = setInterval(() => {
      this.currentIndex = (this.currentIndex + 1) % this.playlist.items.length;
    }, this.SLIDE_DURATION);
  }

  stopSlideshow() {
    if (this.slideshowInterval) {
      clearInterval(this.slideshowInterval);
      this.slideshowInterval = null;
    }
  }

  onPreview(event: Event) {
    event.stopPropagation();
    this.preview.emit(this.playlist);
  }

  onEdit(event: Event) {
    event.stopPropagation();
    this.edit.emit(this.playlist);
  }

  onDelete(event: Event) {
    event.stopPropagation();
    this.delete.emit(this.playlist);
  }

  getItemIcon(type: string): string {
    switch (type) {
      case 'video': return 'movie';
      case 'image': return 'image';
      case 'webpage': return 'web';
      case 'ticker': return 'text_snippet';
      default: return 'insert_drive_file';
    }
  }

  getStatusBackgroundColor(status: string): string {
    switch (status) {
      case 'active': return '#10B981';  // emerald-500
      case 'draft': return '#6B7280';   // gray-500
      case 'archived': return '#F59E0B'; // amber-500
      default: return '#6B7280';        // gray-500
    }
  }

  getPlaylistColor(): string {
    return this.playlist.color || '#3B82F6'; // Use playlist color or default to blue
  }

  getPlaylistColorWithOpacity(opacity: number): string {
    const color = this.playlist.color || '#3B82F6';
    // Convert hex to rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  }

  getTimeAgo(date: string): string {
    const now = new Date();
    const updatedDate = new Date(date);
    const seconds = Math.floor((now.getTime() - updatedDate.getTime()) / 1000);

    if (seconds < 60) return 'just now';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    if (days < 30) return `${days}d ago`;
    const months = Math.floor(days / 30);
    return `${months}mo ago`;
  }

  // Color picker methods
  onColorClick(event: Event): void {
    event.stopPropagation();
    this.openColorPicker();
  }

  openColorPicker(): void {
    this.colorPickerModal = {
      show: true,
      selectedColor: this.playlist.color || '#3B82F6'
    };
  }
  
  closeColorPicker(): void {
    this.colorPickerModal = {
      show: false,
      selectedColor: '#3B82F6'
    };
  }
  
  selectColor(color: string): void {
    this.colorPickerModal.selectedColor = color;
  }
  
  savePlaylistColor(): void {
    const newColor = this.colorPickerModal.selectedColor;
    
    // Update the playlist with the new color
    this.playlistService.updatePlaylist(this.playlist.id, { color: newColor }).subscribe({
      next: (updatedPlaylist) => {
        console.log('Playlist color updated successfully:', updatedPlaylist);
        
        // Update the local playlist object
        this.playlist = updatedPlaylist;
        
        // Emit the change event
        this.colorChanged.emit(updatedPlaylist);
        
        // Close the color picker
        this.closeColorPicker();
      },
      error: (error) => {
        console.error('Error updating playlist color:', error);
        // In a real app, you might want to show an error message to the user
      }
    });
  }
}