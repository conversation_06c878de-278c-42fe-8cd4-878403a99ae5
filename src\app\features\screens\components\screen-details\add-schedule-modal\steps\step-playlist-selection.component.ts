import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Playlist } from '../../../../../../models/playlist.model';

@Component({
  selector: 'app-step-playlist-selection',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="space-y-8">

      <!-- Search Input -->
      <div class="relative">
        <span class="material-icons absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg">search</span>
        <input
          type="text"
          [(ngModel)]="searchQuery"
          (ngModelChange)="filterPlaylists()"
          placeholder="Search playlists..."
          class="w-full pl-12 pr-4 py-3 border-0 bg-gray-50 rounded-xl focus:bg-white focus:ring-2 focus:ring-gray-900 text-sm transition-all duration-200"
        />
      </div>


      <!-- Playlist List -->
      <div class="space-y-3 max-h-80 overflow-y-auto">
        @if (filteredPlaylists.length === 0) {
          <div class="text-center py-12">
            <span class="material-icons text-gray-300 text-5xl mb-3">playlist_play</span>
            <p class="text-gray-500 text-sm font-light">
              @if (searchQuery) {
                No playlists match your search
              } @else {
                No playlists available
              }
            </p>
          </div>
        } @else {
          @for (playlist of filteredPlaylists; track playlist.id) {
            <div
              class="group p-3 rounded-xl cursor-pointer transition-all duration-200"
              [class]="getPlaylistCardClass(playlist.id)"
              (click)="selectPlaylist(playlist.id)"
              [attr.aria-selected]="selectedPlaylistId === playlist.id"
              role="option"
            >
              <div class="flex items-center space-x-3">
                <!-- Playlist Thumbnail -->
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                    @if (getPlaylistThumbnail(playlist)) {
                      <img [src]="getPlaylistThumbnail(playlist)" [alt]="playlist.name" class="w-full h-full object-cover">
                    } @else {
                      <span class="material-icons text-gray-400 text-lg">playlist_play</span>
                    }
                  </div>
                </div>

                <!-- Playlist Info -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <h4 class="font-medium text-gray-900 truncate text-sm">{{ playlist.name }}</h4>
                    <!-- Selection Indicator -->
                    <div class="flex-shrink-0 ml-3">
                      <div
                        class="w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200"
                        [class]="selectedPlaylistId === playlist.id ? 'border-gray-900 bg-gray-900' : 'border-gray-300 group-hover:border-gray-400'"
                      >
                        @if (selectedPlaylistId === playlist.id) {
                          <span class="material-icons text-white text-xs">check</span>
                        }
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                    <span>{{ formatDuration(playlist.duration || 0) }}</span>
                    <span>•</span>
                    <span>{{ playlist.items.length || 0 }} items</span>
                    @if (playlist.status !== 'active') {
                      <span>•</span>
                      <span class="text-xs text-gray-400">{{ playlist.status }}</span>
                    }
                  </div>
                </div>
              </div>
            </div>
          }
        }
      </div>

      <!-- Selected Playlist Summary -->
      @if (selectedPlaylist) {
        <div class="bg-gray-100 rounded-xl p-3 border border-gray-200">
          <div class="flex items-center space-x-3">
            <!-- Thumbnail -->
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gray-300 rounded-lg flex items-center justify-center overflow-hidden">
                @if (getPlaylistThumbnail(selectedPlaylist)) {
                  <img [src]="getPlaylistThumbnail(selectedPlaylist)" [alt]="selectedPlaylist.name" class="w-full h-full object-cover">
                } @else {
                  <span class="material-icons text-gray-500 text-base">playlist_play</span>
                }
              </div>
            </div>

            <!-- Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center">
                <span class="material-icons text-gray-900 mr-2 text-base">check_circle</span>
                <h4 class="font-medium text-gray-900 text-sm truncate">{{ selectedPlaylist.name }}</h4>
              </div>
              <p class="text-xs text-gray-600 mt-1">
                {{ formatDuration(selectedPlaylist.duration || 0) }} • {{ selectedPlaylist.items.length || 0 }} items
              </p>
            </div>
          </div>
        </div>
      }
    </div>
  `
})
export class StepPlaylistSelectionComponent implements OnInit, OnChanges {
  @Input() availablePlaylists: Playlist[] = [];
  @Input() selectedPlaylistId: string = '';
  @Input() error: string | undefined;
  @Output() playlistSelected = new EventEmitter<string>();

  searchQuery = '';
  statusFilter = 'all';
  filteredPlaylists: Playlist[] = [];
  selectedPlaylist: Playlist | null = null;

  ngOnInit(): void {
    this.filterPlaylists();
    this.updateSelectedPlaylist();
  }

  ngOnChanges(): void {
    this.filterPlaylists();
    this.updateSelectedPlaylist();
  }

  filterPlaylists(): void {
    let filtered = [...this.availablePlaylists];

    // Filter by search query
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(playlist =>
        playlist.name.toLowerCase().includes(query) ||
        (playlist.description && playlist.description.toLowerCase().includes(query))
      );
    }

    // Filter by status
    if (this.statusFilter !== 'all') {
      filtered = filtered.filter(playlist => playlist.status === this.statusFilter);
    }

    this.filteredPlaylists = filtered;
  }

  selectPlaylist(playlistId: string): void {
    this.selectedPlaylistId = playlistId;
    this.updateSelectedPlaylist();
    this.playlistSelected.emit(playlistId);
  }

  previewPlaylist(playlist: Playlist, event: Event): void {
    event.stopPropagation();
    // TODO: Implement playlist preview functionality
    console.log('Preview playlist:', playlist);
  }

  private updateSelectedPlaylist(): void {
    this.selectedPlaylist = this.availablePlaylists.find(p => p.id === this.selectedPlaylistId) || null;
  }

  getPlaylistCardClass(playlistId: string): string {
    const isSelected = this.selectedPlaylistId === playlistId;
    const playlist = this.availablePlaylists.find(p => p.id === playlistId);

    if (isSelected) {
      return 'bg-gray-100 border border-gray-200';
    }

    // Add visual indicators for playlist status
    if (playlist?.status !== 'active') {
      return 'bg-gray-50 hover:bg-gray-100 border border-transparent opacity-60';
    }

    if (!playlist?.items || playlist.items.length === 0) {
      return 'bg-gray-50 hover:bg-gray-100 border border-transparent opacity-60';
    }

    return 'bg-gray-50 hover:bg-gray-100 border border-transparent';
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-gray-100 text-gray-700';
      case 'draft':
        return 'bg-gray-100 text-gray-600';
      case 'archived':
        return 'bg-gray-100 text-gray-500';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  }

  formatDuration(seconds: number): string {
    if (!seconds || seconds === 0) return '0:00';

    if (seconds < 60) {
      return `0:${seconds.toString().padStart(2, '0')}`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}:${minutes.toString().padStart(2, '0')}:00`;
    }
  }

  getPlaylistThumbnail(playlist: Playlist): string | null {
    // Get thumbnail from the first item that has one
    if (playlist.items && playlist.items.length > 0) {
      for (const item of playlist.items) {
        if (item.content?.thumbnail) {
          return item.content.thumbnail;
        }
      }
    }
    return null;
  }
}
